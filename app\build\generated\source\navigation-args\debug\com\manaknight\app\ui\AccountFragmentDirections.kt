package com.manaknight.app.ui

import Manaknight.R
import androidx.navigation.ActionOnlyNavDirections
import androidx.navigation.NavDirections

public class AccountFragmentDirections private constructor() {
  public companion object {
    public fun actionAccountFragmentToViewProfileFragment(): NavDirections =
        ActionOnlyNavDirections(R.id.action_accountFragment_to_viewProfileFragment)

    public fun actionAccountFragmentToPlanAndBillingFragment(): NavDirections =
        ActionOnlyNavDirections(R.id.action_accountFragment_to_planAndBillingFragment)
  }
}
