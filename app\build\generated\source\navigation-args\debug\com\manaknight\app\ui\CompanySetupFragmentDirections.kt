package com.manaknight.app.ui

import Manaknight.R
import androidx.navigation.ActionOnlyNavDirections
import androidx.navigation.NavDirections

public class CompanySetupFragmentDirections private constructor() {
  public companion object {
    public fun actionCompanysetupFragmentToMaterialsetupFragment(): NavDirections =
        ActionOnlyNavDirections(R.id.action_companysetupFragment_to_materialsetupFragment)
  }
}
