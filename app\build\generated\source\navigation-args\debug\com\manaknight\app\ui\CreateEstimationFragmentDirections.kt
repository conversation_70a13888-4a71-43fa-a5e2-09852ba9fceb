package com.manaknight.app.ui

import Manaknight.R
import android.os.Bundle
import androidx.navigation.NavDirections
import kotlin.Int
import kotlin.String

public class CreateEstimationFragmentDirections private constructor() {
  private data class ActionCreateEstimationViewToCreateCustomerView(
    public val customerID: Int = 0,
    public val customerName: String = "",
    public val customerAddress: String = "",
    public val customerEmail: String = "",
    public val customerPhoneNumber: String = ""
  ) : NavDirections {
    public override val actionId: Int = R.id.action_createEstimationView_to_createCustomerView

    public override val arguments: Bundle
      get() {
        val result = Bundle()
        result.putInt("customerID", this.customerID)
        result.putString("customerName", this.customerName)
        result.putString("customerAddress", this.customerAddress)
        result.putString("customerEmail", this.customerEmail)
        result.putString("customerPhoneNumber", this.customerPhoneNumber)
        return result
      }
  }

  private data class ActionCreateEstimationViewToLineItemView(
    public val projectID: Int = 0,
    public val customerName: String = ""
  ) : NavDirections {
    public override val actionId: Int = R.id.action_createEstimationView_to_lineItemView

    public override val arguments: Bundle
      get() {
        val result = Bundle()
        result.putInt("projectID", this.projectID)
        result.putString("customerName", this.customerName)
        return result
      }
  }

  public companion object {
    public fun actionCreateEstimationViewToCreateCustomerView(
      customerID: Int = 0,
      customerName: String = "",
      customerAddress: String = "",
      customerEmail: String = "",
      customerPhoneNumber: String = ""
    ): NavDirections = ActionCreateEstimationViewToCreateCustomerView(customerID, customerName,
        customerAddress, customerEmail, customerPhoneNumber)

    public fun actionCreateEstimationViewToLineItemView(projectID: Int = 0, customerName: String =
        ""): NavDirections = ActionCreateEstimationViewToLineItemView(projectID, customerName)
  }
}
