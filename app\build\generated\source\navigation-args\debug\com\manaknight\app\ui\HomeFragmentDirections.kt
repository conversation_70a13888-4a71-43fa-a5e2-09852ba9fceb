package com.manaknight.app.ui

import Manaknight.R
import android.os.Bundle
import androidx.navigation.ActionOnlyNavDirections
import androidx.navigation.NavDirections
import kotlin.Int
import kotlin.String

public class HomeFragmentDirections private constructor() {
  private data class ActionHomeFragmentToProjectsFragment(
    public val selectedStatus: String = "All"
  ) : NavDirections {
    public override val actionId: Int = R.id.action_homeFragment_to_projectsFragment

    public override val arguments: Bundle
      get() {
        val result = Bundle()
        result.putString("selectedStatus", this.selectedStatus)
        return result
      }
  }

  public companion object {
    public fun actionHomeFragmentToCreateEstimationView(): NavDirections =
        ActionOnlyNavDirections(R.id.action_homeFragment_to_createEstimationView)

    public fun actionHomeFragmentToDashboardview(): NavDirections =
        ActionOnlyNavDirections(R.id.action_homeFragment_to_dashboardview)

    public fun actionHomeFragmentToProjectsFragment(selectedStatus: String = "All"): NavDirections =
        ActionHomeFragmentToProjectsFragment(selectedStatus)
  }
}
