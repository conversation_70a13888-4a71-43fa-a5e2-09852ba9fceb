package com.manaknight.app.ui

import Manaknight.R
import androidx.navigation.ActionOnlyNavDirections
import androidx.navigation.NavDirections

public class LinealSetupFragmentDirections private constructor() {
  public companion object {
    public fun actionLinealsetupFragmentTosquaresetupFragment(): NavDirections =
        ActionOnlyNavDirections(R.id.action_linealsetupFragment_tosquaresetupFragment)
  }
}
