package com.manaknight.app.ui

import android.os.Bundle
import androidx.lifecycle.SavedStateHandle
import androidx.navigation.NavArgs
import java.lang.IllegalArgumentException
import kotlin.String
import kotlin.jvm.JvmStatic

public data class ProjectsFragmentArgs(
  public val selectedStatus: String = "All"
) : NavArgs {
  public fun toBundle(): Bundle {
    val result = Bundle()
    result.putString("selectedStatus", this.selectedStatus)
    return result
  }

  public fun toSavedStateHandle(): SavedStateHandle {
    val result = SavedStateHandle()
    result.set("selectedStatus", this.selectedStatus)
    return result
  }

  public companion object {
    @JvmStatic
    public fun fromBundle(bundle: Bundle): ProjectsFragmentArgs {
      bundle.setClassLoader(ProjectsFragmentArgs::class.java.classLoader)
      val __selectedStatus : String?
      if (bundle.containsKey("selectedStatus")) {
        __selectedStatus = bundle.getString("selectedStatus")
        if (__selectedStatus == null) {
          throw IllegalArgumentException("Argument \"selectedStatus\" is marked as non-null but was passed a null value.")
        }
      } else {
        __selectedStatus = "All"
      }
      return ProjectsFragmentArgs(__selectedStatus)
    }

    @JvmStatic
    public fun fromSavedStateHandle(savedStateHandle: SavedStateHandle): ProjectsFragmentArgs {
      val __selectedStatus : String?
      if (savedStateHandle.contains("selectedStatus")) {
        __selectedStatus = savedStateHandle["selectedStatus"]
        if (__selectedStatus == null) {
          throw IllegalArgumentException("Argument \"selectedStatus\" is marked as non-null but was passed a null value")
        }
      } else {
        __selectedStatus = "All"
      }
      return ProjectsFragmentArgs(__selectedStatus)
    }
  }
}
