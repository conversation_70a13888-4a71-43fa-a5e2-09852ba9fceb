package com.manaknight.app.ui

import Manaknight.R
import android.os.Bundle
import androidx.navigation.ActionOnlyNavDirections
import androidx.navigation.NavDirections
import kotlin.Int
import kotlin.String

public class ProjectsFragmentDirections private constructor() {
  private data class ActionProjectsFragmentToProjectChangeOrderFragment(
    public val projectID: Int = 0,
    public val customerName: String = ""
  ) : NavDirections {
    public override val actionId: Int = R.id.action_projectsFragment_to_projectChangeOrderFragment

    public override val arguments: Bundle
      get() {
        val result = Bundle()
        result.putInt("projectID", this.projectID)
        result.putString("customerName", this.customerName)
        return result
      }
  }

  private data class ActionProjectsFragmentToProjectViewEstimateFragment(
    public val projectID: Int = 0
  ) : NavDirections {
    public override val actionId: Int = R.id.action_projectsFragment_to_projectViewEstimateFragment

    public override val arguments: Bundle
      get() {
        val result = Bundle()
        result.putInt("projectID", this.projectID)
        return result
      }
  }

  public companion object {
    public fun actionProjectsFragmentToCreateEstimationView(): NavDirections =
        ActionOnlyNavDirections(R.id.action_projectsFragment_to_createEstimationView)

    public fun actionProjectsFragmentToProjectDetailsFragment(): NavDirections =
        ActionOnlyNavDirections(R.id.action_projectsFragment_to_projectDetailsFragment)

    public fun actionProjectsFragmentToProjectChangeOrderFragment(projectID: Int = 0,
        customerName: String = ""): NavDirections =
        ActionProjectsFragmentToProjectChangeOrderFragment(projectID, customerName)

    public fun actionProjectsFragmentToProjectViewEstimateFragment(projectID: Int = 0):
        NavDirections = ActionProjectsFragmentToProjectViewEstimateFragment(projectID)
  }
}
