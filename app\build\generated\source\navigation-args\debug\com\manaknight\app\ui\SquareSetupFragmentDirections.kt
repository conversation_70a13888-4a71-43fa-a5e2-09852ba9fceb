package com.manaknight.app.ui

import Manaknight.R
import androidx.navigation.ActionOnlyNavDirections
import androidx.navigation.NavDirections

public class SquareSetupFragmentDirections private constructor() {
  public companion object {
    public fun actionSquaresetupFragmentTofinishsetupFragment(): NavDirections =
        ActionOnlyNavDirections(R.id.action_squaresetupFragment_tofinishsetupFragment)
  }
}
