package com.manaknight.app.ui

import Manaknight.R
import androidx.navigation.ActionOnlyNavDirections
import androidx.navigation.NavDirections

public class SubscriptionFragmentDirections private constructor() {
  public companion object {
    public fun actionSubscriptonFragmentToCompanySetupFragment(): NavDirections =
        ActionOnlyNavDirections(R.id.action_subscriptonFragment_to_companySetupFragment)
  }
}
