package com.manaknight.app.ui.fragments

import android.os.Bundle
import android.os.Parcelable
import androidx.lifecycle.SavedStateHandle
import androidx.navigation.NavArgs
import com.manaknight.app.model.remote.profitPro.MaterialRespListModel2
import java.io.Serializable
import java.lang.IllegalArgumentException
import java.lang.UnsupportedOperationException
import kotlin.Int
import kotlin.String
import kotlin.Suppress
import kotlin.jvm.JvmStatic

public data class AddEditLinealLineItemFragmentArgs(
  public val materialItem: MaterialRespListModel2,
  public val projectID: Int = 0,
  public val lineItem: Int = 0,
  public val lineDescrition: String = "0",
  public val lineEstimateType: String = "0",
  public val isEditable: Int = 0,
  public val labourHours: String = "0",
  public val itemLineID: Int = 0
) : NavArgs {
  @Suppress("CAST_NEVER_SUCCEEDS")
  public fun toBundle(): Bundle {
    val result = Bundle()
    result.putInt("projectID", this.projectID)
    result.putInt("lineItem", this.lineItem)
    result.putString("lineDescrition", this.lineDescrition)
    result.putString("lineEstimateType", this.lineEstimateType)
    result.putInt("isEditable", this.isEditable)
    result.putString("labourHours", this.labourHours)
    result.putInt("itemLineID", this.itemLineID)
    if (Parcelable::class.java.isAssignableFrom(MaterialRespListModel2::class.java)) {
      result.putParcelable("materialItem", this.materialItem as Parcelable)
    } else if (Serializable::class.java.isAssignableFrom(MaterialRespListModel2::class.java)) {
      result.putSerializable("materialItem", this.materialItem as Serializable)
    } else {
      throw UnsupportedOperationException(MaterialRespListModel2::class.java.name +
          " must implement Parcelable or Serializable or must be an Enum.")
    }
    return result
  }

  @Suppress("CAST_NEVER_SUCCEEDS")
  public fun toSavedStateHandle(): SavedStateHandle {
    val result = SavedStateHandle()
    result.set("projectID", this.projectID)
    result.set("lineItem", this.lineItem)
    result.set("lineDescrition", this.lineDescrition)
    result.set("lineEstimateType", this.lineEstimateType)
    result.set("isEditable", this.isEditable)
    result.set("labourHours", this.labourHours)
    result.set("itemLineID", this.itemLineID)
    if (Parcelable::class.java.isAssignableFrom(MaterialRespListModel2::class.java)) {
      result.set("materialItem", this.materialItem as Parcelable)
    } else if (Serializable::class.java.isAssignableFrom(MaterialRespListModel2::class.java)) {
      result.set("materialItem", this.materialItem as Serializable)
    } else {
      throw UnsupportedOperationException(MaterialRespListModel2::class.java.name +
          " must implement Parcelable or Serializable or must be an Enum.")
    }
    return result
  }

  public companion object {
    @JvmStatic
    @Suppress("DEPRECATION")
    public fun fromBundle(bundle: Bundle): AddEditLinealLineItemFragmentArgs {
      bundle.setClassLoader(AddEditLinealLineItemFragmentArgs::class.java.classLoader)
      val __projectID : Int
      if (bundle.containsKey("projectID")) {
        __projectID = bundle.getInt("projectID")
      } else {
        __projectID = 0
      }
      val __lineItem : Int
      if (bundle.containsKey("lineItem")) {
        __lineItem = bundle.getInt("lineItem")
      } else {
        __lineItem = 0
      }
      val __lineDescrition : String?
      if (bundle.containsKey("lineDescrition")) {
        __lineDescrition = bundle.getString("lineDescrition")
        if (__lineDescrition == null) {
          throw IllegalArgumentException("Argument \"lineDescrition\" is marked as non-null but was passed a null value.")
        }
      } else {
        __lineDescrition = "0"
      }
      val __lineEstimateType : String?
      if (bundle.containsKey("lineEstimateType")) {
        __lineEstimateType = bundle.getString("lineEstimateType")
        if (__lineEstimateType == null) {
          throw IllegalArgumentException("Argument \"lineEstimateType\" is marked as non-null but was passed a null value.")
        }
      } else {
        __lineEstimateType = "0"
      }
      val __isEditable : Int
      if (bundle.containsKey("isEditable")) {
        __isEditable = bundle.getInt("isEditable")
      } else {
        __isEditable = 0
      }
      val __labourHours : String?
      if (bundle.containsKey("labourHours")) {
        __labourHours = bundle.getString("labourHours")
        if (__labourHours == null) {
          throw IllegalArgumentException("Argument \"labourHours\" is marked as non-null but was passed a null value.")
        }
      } else {
        __labourHours = "0"
      }
      val __itemLineID : Int
      if (bundle.containsKey("itemLineID")) {
        __itemLineID = bundle.getInt("itemLineID")
      } else {
        __itemLineID = 0
      }
      val __materialItem : MaterialRespListModel2?
      if (bundle.containsKey("materialItem")) {
        if (Parcelable::class.java.isAssignableFrom(MaterialRespListModel2::class.java) ||
            Serializable::class.java.isAssignableFrom(MaterialRespListModel2::class.java)) {
          __materialItem = bundle.get("materialItem") as MaterialRespListModel2?
        } else {
          throw UnsupportedOperationException(MaterialRespListModel2::class.java.name +
              " must implement Parcelable or Serializable or must be an Enum.")
        }
        if (__materialItem == null) {
          throw IllegalArgumentException("Argument \"materialItem\" is marked as non-null but was passed a null value.")
        }
      } else {
        throw IllegalArgumentException("Required argument \"materialItem\" is missing and does not have an android:defaultValue")
      }
      return AddEditLinealLineItemFragmentArgs(__materialItem, __projectID, __lineItem,
          __lineDescrition, __lineEstimateType, __isEditable, __labourHours, __itemLineID)
    }

    @JvmStatic
    public fun fromSavedStateHandle(savedStateHandle: SavedStateHandle):
        AddEditLinealLineItemFragmentArgs {
      val __projectID : Int?
      if (savedStateHandle.contains("projectID")) {
        __projectID = savedStateHandle["projectID"]
        if (__projectID == null) {
          throw IllegalArgumentException("Argument \"projectID\" of type integer does not support null values")
        }
      } else {
        __projectID = 0
      }
      val __lineItem : Int?
      if (savedStateHandle.contains("lineItem")) {
        __lineItem = savedStateHandle["lineItem"]
        if (__lineItem == null) {
          throw IllegalArgumentException("Argument \"lineItem\" of type integer does not support null values")
        }
      } else {
        __lineItem = 0
      }
      val __lineDescrition : String?
      if (savedStateHandle.contains("lineDescrition")) {
        __lineDescrition = savedStateHandle["lineDescrition"]
        if (__lineDescrition == null) {
          throw IllegalArgumentException("Argument \"lineDescrition\" is marked as non-null but was passed a null value")
        }
      } else {
        __lineDescrition = "0"
      }
      val __lineEstimateType : String?
      if (savedStateHandle.contains("lineEstimateType")) {
        __lineEstimateType = savedStateHandle["lineEstimateType"]
        if (__lineEstimateType == null) {
          throw IllegalArgumentException("Argument \"lineEstimateType\" is marked as non-null but was passed a null value")
        }
      } else {
        __lineEstimateType = "0"
      }
      val __isEditable : Int?
      if (savedStateHandle.contains("isEditable")) {
        __isEditable = savedStateHandle["isEditable"]
        if (__isEditable == null) {
          throw IllegalArgumentException("Argument \"isEditable\" of type integer does not support null values")
        }
      } else {
        __isEditable = 0
      }
      val __labourHours : String?
      if (savedStateHandle.contains("labourHours")) {
        __labourHours = savedStateHandle["labourHours"]
        if (__labourHours == null) {
          throw IllegalArgumentException("Argument \"labourHours\" is marked as non-null but was passed a null value")
        }
      } else {
        __labourHours = "0"
      }
      val __itemLineID : Int?
      if (savedStateHandle.contains("itemLineID")) {
        __itemLineID = savedStateHandle["itemLineID"]
        if (__itemLineID == null) {
          throw IllegalArgumentException("Argument \"itemLineID\" of type integer does not support null values")
        }
      } else {
        __itemLineID = 0
      }
      val __materialItem : MaterialRespListModel2?
      if (savedStateHandle.contains("materialItem")) {
        if (Parcelable::class.java.isAssignableFrom(MaterialRespListModel2::class.java) ||
            Serializable::class.java.isAssignableFrom(MaterialRespListModel2::class.java)) {
          __materialItem = savedStateHandle.get<MaterialRespListModel2?>("materialItem")
        } else {
          throw UnsupportedOperationException(MaterialRespListModel2::class.java.name +
              " must implement Parcelable or Serializable or must be an Enum.")
        }
        if (__materialItem == null) {
          throw IllegalArgumentException("Argument \"materialItem\" is marked as non-null but was passed a null value")
        }
      } else {
        throw IllegalArgumentException("Required argument \"materialItem\" is missing and does not have an android:defaultValue")
      }
      return AddEditLinealLineItemFragmentArgs(__materialItem, __projectID, __lineItem,
          __lineDescrition, __lineEstimateType, __isEditable, __labourHours, __itemLineID)
    }
  }
}
