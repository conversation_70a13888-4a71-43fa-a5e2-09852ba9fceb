package com.manaknight.app.ui.fragments

import android.os.Bundle
import androidx.lifecycle.SavedStateHandle
import androidx.navigation.NavArgs
import java.lang.IllegalArgumentException
import kotlin.Int
import kotlin.String
import kotlin.jvm.JvmStatic

public data class AddLineItemComposeFragmentArgs(
  public val projectID: Int = 0,
  public val isEdit: Int = 0,
  public val customerName: String = " "
) : NavArgs {
  public fun toBundle(): Bundle {
    val result = Bundle()
    result.putInt("projectID", this.projectID)
    result.putInt("isEdit", this.isEdit)
    result.putString("customerName", this.customerName)
    return result
  }

  public fun toSavedStateHandle(): SavedStateHandle {
    val result = SavedStateHandle()
    result.set("projectID", this.projectID)
    result.set("isEdit", this.isEdit)
    result.set("customerName", this.customerName)
    return result
  }

  public companion object {
    @JvmStatic
    public fun fromBundle(bundle: Bundle): AddLineItemComposeFragmentArgs {
      bundle.setClassLoader(AddLineItemComposeFragmentArgs::class.java.classLoader)
      val __projectID : Int
      if (bundle.containsKey("projectID")) {
        __projectID = bundle.getInt("projectID")
      } else {
        __projectID = 0
      }
      val __isEdit : Int
      if (bundle.containsKey("isEdit")) {
        __isEdit = bundle.getInt("isEdit")
      } else {
        __isEdit = 0
      }
      val __customerName : String?
      if (bundle.containsKey("customerName")) {
        __customerName = bundle.getString("customerName")
        if (__customerName == null) {
          throw IllegalArgumentException("Argument \"customerName\" is marked as non-null but was passed a null value.")
        }
      } else {
        __customerName = " "
      }
      return AddLineItemComposeFragmentArgs(__projectID, __isEdit, __customerName)
    }

    @JvmStatic
    public fun fromSavedStateHandle(savedStateHandle: SavedStateHandle):
        AddLineItemComposeFragmentArgs {
      val __projectID : Int?
      if (savedStateHandle.contains("projectID")) {
        __projectID = savedStateHandle["projectID"]
        if (__projectID == null) {
          throw IllegalArgumentException("Argument \"projectID\" of type integer does not support null values")
        }
      } else {
        __projectID = 0
      }
      val __isEdit : Int?
      if (savedStateHandle.contains("isEdit")) {
        __isEdit = savedStateHandle["isEdit"]
        if (__isEdit == null) {
          throw IllegalArgumentException("Argument \"isEdit\" of type integer does not support null values")
        }
      } else {
        __isEdit = 0
      }
      val __customerName : String?
      if (savedStateHandle.contains("customerName")) {
        __customerName = savedStateHandle["customerName"]
        if (__customerName == null) {
          throw IllegalArgumentException("Argument \"customerName\" is marked as non-null but was passed a null value")
        }
      } else {
        __customerName = " "
      }
      return AddLineItemComposeFragmentArgs(__projectID, __isEdit, __customerName)
    }
  }
}
