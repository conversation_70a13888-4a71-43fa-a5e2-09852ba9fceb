package com.manaknight.app.ui.fragments

import Manaknight.R
import android.os.Bundle
import android.os.Parcelable
import androidx.navigation.NavDirections
import com.manaknight.app.model.remote.profitPro.MaterialRespListModel2
import java.io.Serializable
import java.lang.UnsupportedOperationException
import kotlin.Int
import kotlin.String
import kotlin.Suppress

public class AddLineItemComposeFragmentDirections private constructor() {
  private data class ActionAddlineItemViewComposeToAddMateriallineItemViewCompose(
    public val materialItem: MaterialRespListModel2,
    public val projectID: Int = 0,
    public val lineItem: Int = 0,
    public val lineDescrition: String = "0",
    public val lineEstimateType: String = "0",
    public val isEditable: Int = 0,
    public val labourHours: String = "0",
    public val itemLineID: Int = 0
  ) : NavDirections {
    public override val actionId: Int =
        R.id.action_addlineItemViewCompose_to_addMateriallineItemViewCompose

    public override val arguments: Bundle
      @Suppress("CAST_NEVER_SUCCEEDS")
      get() {
        val result = Bundle()
        result.putInt("projectID", this.projectID)
        result.putInt("lineItem", this.lineItem)
        result.putString("lineDescrition", this.lineDescrition)
        result.putString("lineEstimateType", this.lineEstimateType)
        result.putInt("isEditable", this.isEditable)
        result.putString("labourHours", this.labourHours)
        result.putInt("itemLineID", this.itemLineID)
        if (Parcelable::class.java.isAssignableFrom(MaterialRespListModel2::class.java)) {
          result.putParcelable("materialItem", this.materialItem as Parcelable)
        } else if (Serializable::class.java.isAssignableFrom(MaterialRespListModel2::class.java)) {
          result.putSerializable("materialItem", this.materialItem as Serializable)
        } else {
          throw UnsupportedOperationException(MaterialRespListModel2::class.java.name +
              " must implement Parcelable or Serializable or must be an Enum.")
        }
        return result
      }
  }

  private data class ActionAddlineItemViewComposeToAddLinearlineItemViewCompose(
    public val materialItem: MaterialRespListModel2,
    public val projectID: Int = 0,
    public val lineItem: Int = 0,
    public val lineDescrition: String = "0",
    public val lineEstimateType: String = "0",
    public val isEditable: Int = 0,
    public val labourHours: String = "0",
    public val itemLineID: Int = 0
  ) : NavDirections {
    public override val actionId: Int =
        R.id.action_addlineItemViewCompose_to_addLinearlineItemViewCompose

    public override val arguments: Bundle
      @Suppress("CAST_NEVER_SUCCEEDS")
      get() {
        val result = Bundle()
        result.putInt("projectID", this.projectID)
        result.putInt("lineItem", this.lineItem)
        result.putString("lineDescrition", this.lineDescrition)
        result.putString("lineEstimateType", this.lineEstimateType)
        result.putInt("isEditable", this.isEditable)
        result.putString("labourHours", this.labourHours)
        result.putInt("itemLineID", this.itemLineID)
        if (Parcelable::class.java.isAssignableFrom(MaterialRespListModel2::class.java)) {
          result.putParcelable("materialItem", this.materialItem as Parcelable)
        } else if (Serializable::class.java.isAssignableFrom(MaterialRespListModel2::class.java)) {
          result.putSerializable("materialItem", this.materialItem as Serializable)
        } else {
          throw UnsupportedOperationException(MaterialRespListModel2::class.java.name +
              " must implement Parcelable or Serializable or must be an Enum.")
        }
        return result
      }
  }

  public companion object {
    public fun actionAddlineItemViewComposeToAddMateriallineItemViewCompose(
      materialItem: MaterialRespListModel2,
      projectID: Int = 0,
      lineItem: Int = 0,
      lineDescrition: String = "0",
      lineEstimateType: String = "0",
      isEditable: Int = 0,
      labourHours: String = "0",
      itemLineID: Int = 0
    ): NavDirections = ActionAddlineItemViewComposeToAddMateriallineItemViewCompose(materialItem,
        projectID, lineItem, lineDescrition, lineEstimateType, isEditable, labourHours, itemLineID)

    public fun actionAddlineItemViewComposeToAddLinearlineItemViewCompose(
      materialItem: MaterialRespListModel2,
      projectID: Int = 0,
      lineItem: Int = 0,
      lineDescrition: String = "0",
      lineEstimateType: String = "0",
      isEditable: Int = 0,
      labourHours: String = "0",
      itemLineID: Int = 0
    ): NavDirections = ActionAddlineItemViewComposeToAddLinearlineItemViewCompose(materialItem,
        projectID, lineItem, lineDescrition, lineEstimateType, isEditable, labourHours, itemLineID)
  }
}
