package com.manaknight.app.ui.fragments

import android.os.Bundle
import android.os.Parcelable
import androidx.lifecycle.SavedStateHandle
import androidx.navigation.NavArgs
import com.manaknight.app.model.remote.profitPro.JobDetailsRespModel
import java.io.Serializable
import java.lang.IllegalArgumentException
import java.lang.UnsupportedOperationException
import kotlin.Int
import kotlin.String
import kotlin.Suppress
import kotlin.jvm.JvmStatic

public data class EditLineItemComposeFragmentArgs(
  public val item: JobDetailsRespModel,
  public val projectID: Int = 0,
  public val customerName: String = "",
  public val lineItemID: Int = 0,
  public val initialDescription: String = "",
  public val initialType: String = ""
) : NavArgs {
  @Suppress("CAST_NEVER_SUCCEEDS")
  public fun toBundle(): Bundle {
    val result = Bundle()
    result.putInt("projectID", this.projectID)
    result.putString("customerName", this.customerName)
    result.putInt("lineItemID", this.lineItemID)
    result.putString("initialDescription", this.initialDescription)
    result.putString("initialType", this.initialType)
    if (Parcelable::class.java.isAssignableFrom(JobDetailsRespModel::class.java)) {
      result.putParcelable("item", this.item as Parcelable)
    } else if (Serializable::class.java.isAssignableFrom(JobDetailsRespModel::class.java)) {
      result.putSerializable("item", this.item as Serializable)
    } else {
      throw UnsupportedOperationException(JobDetailsRespModel::class.java.name +
          " must implement Parcelable or Serializable or must be an Enum.")
    }
    return result
  }

  @Suppress("CAST_NEVER_SUCCEEDS")
  public fun toSavedStateHandle(): SavedStateHandle {
    val result = SavedStateHandle()
    result.set("projectID", this.projectID)
    result.set("customerName", this.customerName)
    result.set("lineItemID", this.lineItemID)
    result.set("initialDescription", this.initialDescription)
    result.set("initialType", this.initialType)
    if (Parcelable::class.java.isAssignableFrom(JobDetailsRespModel::class.java)) {
      result.set("item", this.item as Parcelable)
    } else if (Serializable::class.java.isAssignableFrom(JobDetailsRespModel::class.java)) {
      result.set("item", this.item as Serializable)
    } else {
      throw UnsupportedOperationException(JobDetailsRespModel::class.java.name +
          " must implement Parcelable or Serializable or must be an Enum.")
    }
    return result
  }

  public companion object {
    @JvmStatic
    @Suppress("DEPRECATION")
    public fun fromBundle(bundle: Bundle): EditLineItemComposeFragmentArgs {
      bundle.setClassLoader(EditLineItemComposeFragmentArgs::class.java.classLoader)
      val __projectID : Int
      if (bundle.containsKey("projectID")) {
        __projectID = bundle.getInt("projectID")
      } else {
        __projectID = 0
      }
      val __customerName : String?
      if (bundle.containsKey("customerName")) {
        __customerName = bundle.getString("customerName")
        if (__customerName == null) {
          throw IllegalArgumentException("Argument \"customerName\" is marked as non-null but was passed a null value.")
        }
      } else {
        __customerName = ""
      }
      val __lineItemID : Int
      if (bundle.containsKey("lineItemID")) {
        __lineItemID = bundle.getInt("lineItemID")
      } else {
        __lineItemID = 0
      }
      val __initialDescription : String?
      if (bundle.containsKey("initialDescription")) {
        __initialDescription = bundle.getString("initialDescription")
        if (__initialDescription == null) {
          throw IllegalArgumentException("Argument \"initialDescription\" is marked as non-null but was passed a null value.")
        }
      } else {
        __initialDescription = ""
      }
      val __initialType : String?
      if (bundle.containsKey("initialType")) {
        __initialType = bundle.getString("initialType")
        if (__initialType == null) {
          throw IllegalArgumentException("Argument \"initialType\" is marked as non-null but was passed a null value.")
        }
      } else {
        __initialType = ""
      }
      val __item : JobDetailsRespModel?
      if (bundle.containsKey("item")) {
        if (Parcelable::class.java.isAssignableFrom(JobDetailsRespModel::class.java) ||
            Serializable::class.java.isAssignableFrom(JobDetailsRespModel::class.java)) {
          __item = bundle.get("item") as JobDetailsRespModel?
        } else {
          throw UnsupportedOperationException(JobDetailsRespModel::class.java.name +
              " must implement Parcelable or Serializable or must be an Enum.")
        }
        if (__item == null) {
          throw IllegalArgumentException("Argument \"item\" is marked as non-null but was passed a null value.")
        }
      } else {
        throw IllegalArgumentException("Required argument \"item\" is missing and does not have an android:defaultValue")
      }
      return EditLineItemComposeFragmentArgs(__item, __projectID, __customerName, __lineItemID,
          __initialDescription, __initialType)
    }

    @JvmStatic
    public fun fromSavedStateHandle(savedStateHandle: SavedStateHandle):
        EditLineItemComposeFragmentArgs {
      val __projectID : Int?
      if (savedStateHandle.contains("projectID")) {
        __projectID = savedStateHandle["projectID"]
        if (__projectID == null) {
          throw IllegalArgumentException("Argument \"projectID\" of type integer does not support null values")
        }
      } else {
        __projectID = 0
      }
      val __customerName : String?
      if (savedStateHandle.contains("customerName")) {
        __customerName = savedStateHandle["customerName"]
        if (__customerName == null) {
          throw IllegalArgumentException("Argument \"customerName\" is marked as non-null but was passed a null value")
        }
      } else {
        __customerName = ""
      }
      val __lineItemID : Int?
      if (savedStateHandle.contains("lineItemID")) {
        __lineItemID = savedStateHandle["lineItemID"]
        if (__lineItemID == null) {
          throw IllegalArgumentException("Argument \"lineItemID\" of type integer does not support null values")
        }
      } else {
        __lineItemID = 0
      }
      val __initialDescription : String?
      if (savedStateHandle.contains("initialDescription")) {
        __initialDescription = savedStateHandle["initialDescription"]
        if (__initialDescription == null) {
          throw IllegalArgumentException("Argument \"initialDescription\" is marked as non-null but was passed a null value")
        }
      } else {
        __initialDescription = ""
      }
      val __initialType : String?
      if (savedStateHandle.contains("initialType")) {
        __initialType = savedStateHandle["initialType"]
        if (__initialType == null) {
          throw IllegalArgumentException("Argument \"initialType\" is marked as non-null but was passed a null value")
        }
      } else {
        __initialType = ""
      }
      val __item : JobDetailsRespModel?
      if (savedStateHandle.contains("item")) {
        if (Parcelable::class.java.isAssignableFrom(JobDetailsRespModel::class.java) ||
            Serializable::class.java.isAssignableFrom(JobDetailsRespModel::class.java)) {
          __item = savedStateHandle.get<JobDetailsRespModel?>("item")
        } else {
          throw UnsupportedOperationException(JobDetailsRespModel::class.java.name +
              " must implement Parcelable or Serializable or must be an Enum.")
        }
        if (__item == null) {
          throw IllegalArgumentException("Argument \"item\" is marked as non-null but was passed a null value")
        }
      } else {
        throw IllegalArgumentException("Required argument \"item\" is missing and does not have an android:defaultValue")
      }
      return EditLineItemComposeFragmentArgs(__item, __projectID, __customerName, __lineItemID,
          __initialDescription, __initialType)
    }
  }
}
