package com.manaknight.app.ui.fragments

import Manaknight.R
import android.os.Bundle
import android.os.Parcelable
import androidx.navigation.NavDirections
import com.manaknight.app.model.remote.profitPro.JobDetailsRespModel
import com.manaknight.app.model.remote.profitPro.MaterialRespListModel2
import java.io.Serializable
import java.lang.UnsupportedOperationException
import kotlin.Int
import kotlin.String
import kotlin.Suppress

public class LineItemsComposeFragmentDirections private constructor() {
  private data class ActionLineItemViewComposeToPreviewProjectDetailsFragment(
    public val projectID: Int = 0
  ) : NavDirections {
    public override val actionId: Int =
        R.id.action_lineItemViewCompose_to_previewProjectDetailsFragment

    public override val arguments: Bundle
      get() {
        val result = Bundle()
        result.putInt("projectID", this.projectID)
        return result
      }
  }

  private data class ActionLineItemViewComposeToEditlineItemViewCompose(
    public val item: JobDetailsRespModel,
    public val projectID: Int = 0,
    public val customerName: String = "",
    public val lineItemID: Int = 0,
    public val initialDescription: String = "",
    public val initialType: String = ""
  ) : NavDirections {
    public override val actionId: Int = R.id.action_lineItemViewCompose_to_editlineItemViewCompose

    public override val arguments: Bundle
      @Suppress("CAST_NEVER_SUCCEEDS")
      get() {
        val result = Bundle()
        result.putInt("projectID", this.projectID)
        result.putString("customerName", this.customerName)
        result.putInt("lineItemID", this.lineItemID)
        result.putString("initialDescription", this.initialDescription)
        result.putString("initialType", this.initialType)
        if (Parcelable::class.java.isAssignableFrom(JobDetailsRespModel::class.java)) {
          result.putParcelable("item", this.item as Parcelable)
        } else if (Serializable::class.java.isAssignableFrom(JobDetailsRespModel::class.java)) {
          result.putSerializable("item", this.item as Serializable)
        } else {
          throw UnsupportedOperationException(JobDetailsRespModel::class.java.name +
              " must implement Parcelable or Serializable or must be an Enum.")
        }
        return result
      }
  }

  private data class ActionLineItemViewComposeToAddlineItemViewCompose(
    public val projectID: Int = 0,
    public val isEdit: Int = 0,
    public val customerName: String = " "
  ) : NavDirections {
    public override val actionId: Int = R.id.action_lineItemViewCompose_to_addlineItemViewCompose

    public override val arguments: Bundle
      get() {
        val result = Bundle()
        result.putInt("projectID", this.projectID)
        result.putInt("isEdit", this.isEdit)
        result.putString("customerName", this.customerName)
        return result
      }
  }

  private data class ActionLineItemViewComposeToDrawsDetailViewCompose(
    public val projectID: Int = 0,
    public val customerName: String = ""
  ) : NavDirections {
    public override val actionId: Int = R.id.action_lineItemViewCompose_to_drawsDetailViewCompose

    public override val arguments: Bundle
      get() {
        val result = Bundle()
        result.putInt("projectID", this.projectID)
        result.putString("customerName", this.customerName)
        return result
      }
  }

  private data class ActionLineItemViewComposeToAddMateriallineItemViewCompose(
    public val materialItem: MaterialRespListModel2,
    public val projectID: Int = 0,
    public val lineItem: Int = 0,
    public val lineDescrition: String = "0",
    public val lineEstimateType: String = "0",
    public val isEditable: Int = 0,
    public val labourHours: String = "0",
    public val itemLineID: Int = 0
  ) : NavDirections {
    public override val actionId: Int =
        R.id.action_lineItemViewCompose_to_addMateriallineItemViewCompose

    public override val arguments: Bundle
      @Suppress("CAST_NEVER_SUCCEEDS")
      get() {
        val result = Bundle()
        result.putInt("projectID", this.projectID)
        result.putInt("lineItem", this.lineItem)
        result.putString("lineDescrition", this.lineDescrition)
        result.putString("lineEstimateType", this.lineEstimateType)
        result.putInt("isEditable", this.isEditable)
        result.putString("labourHours", this.labourHours)
        result.putInt("itemLineID", this.itemLineID)
        if (Parcelable::class.java.isAssignableFrom(MaterialRespListModel2::class.java)) {
          result.putParcelable("materialItem", this.materialItem as Parcelable)
        } else if (Serializable::class.java.isAssignableFrom(MaterialRespListModel2::class.java)) {
          result.putSerializable("materialItem", this.materialItem as Serializable)
        } else {
          throw UnsupportedOperationException(MaterialRespListModel2::class.java.name +
              " must implement Parcelable or Serializable or must be an Enum.")
        }
        return result
      }
  }

  private data class ActionLineItemViewComposeToAddLinearlineItemViewCompose(
    public val materialItem: MaterialRespListModel2,
    public val projectID: Int = 0,
    public val lineItem: Int = 0,
    public val lineDescrition: String = "0",
    public val lineEstimateType: String = "0",
    public val isEditable: Int = 0,
    public val labourHours: String = "0",
    public val itemLineID: Int = 0
  ) : NavDirections {
    public override val actionId: Int =
        R.id.action_lineItemViewCompose_to_addLinearlineItemViewCompose

    public override val arguments: Bundle
      @Suppress("CAST_NEVER_SUCCEEDS")
      get() {
        val result = Bundle()
        result.putInt("projectID", this.projectID)
        result.putInt("lineItem", this.lineItem)
        result.putString("lineDescrition", this.lineDescrition)
        result.putString("lineEstimateType", this.lineEstimateType)
        result.putInt("isEditable", this.isEditable)
        result.putString("labourHours", this.labourHours)
        result.putInt("itemLineID", this.itemLineID)
        if (Parcelable::class.java.isAssignableFrom(MaterialRespListModel2::class.java)) {
          result.putParcelable("materialItem", this.materialItem as Parcelable)
        } else if (Serializable::class.java.isAssignableFrom(MaterialRespListModel2::class.java)) {
          result.putSerializable("materialItem", this.materialItem as Serializable)
        } else {
          throw UnsupportedOperationException(MaterialRespListModel2::class.java.name +
              " must implement Parcelable or Serializable or must be an Enum.")
        }
        return result
      }
  }

  public companion object {
    public fun actionLineItemViewComposeToPreviewProjectDetailsFragment(projectID: Int = 0):
        NavDirections = ActionLineItemViewComposeToPreviewProjectDetailsFragment(projectID)

    public fun actionLineItemViewComposeToEditlineItemViewCompose(
      item: JobDetailsRespModel,
      projectID: Int = 0,
      customerName: String = "",
      lineItemID: Int = 0,
      initialDescription: String = "",
      initialType: String = ""
    ): NavDirections = ActionLineItemViewComposeToEditlineItemViewCompose(item, projectID,
        customerName, lineItemID, initialDescription, initialType)

    public fun actionLineItemViewComposeToAddlineItemViewCompose(
      projectID: Int = 0,
      isEdit: Int = 0,
      customerName: String = " "
    ): NavDirections = ActionLineItemViewComposeToAddlineItemViewCompose(projectID, isEdit,
        customerName)

    public fun actionLineItemViewComposeToDrawsDetailViewCompose(projectID: Int = 0,
        customerName: String = ""): NavDirections =
        ActionLineItemViewComposeToDrawsDetailViewCompose(projectID, customerName)

    public fun actionLineItemViewComposeToAddMateriallineItemViewCompose(
      materialItem: MaterialRespListModel2,
      projectID: Int = 0,
      lineItem: Int = 0,
      lineDescrition: String = "0",
      lineEstimateType: String = "0",
      isEditable: Int = 0,
      labourHours: String = "0",
      itemLineID: Int = 0
    ): NavDirections = ActionLineItemViewComposeToAddMateriallineItemViewCompose(materialItem,
        projectID, lineItem, lineDescrition, lineEstimateType, isEditable, labourHours, itemLineID)

    public fun actionLineItemViewComposeToAddLinearlineItemViewCompose(
      materialItem: MaterialRespListModel2,
      projectID: Int = 0,
      lineItem: Int = 0,
      lineDescrition: String = "0",
      lineEstimateType: String = "0",
      isEditable: Int = 0,
      labourHours: String = "0",
      itemLineID: Int = 0
    ): NavDirections = ActionLineItemViewComposeToAddLinearlineItemViewCompose(materialItem,
        projectID, lineItem, lineDescrition, lineEstimateType, isEditable, labourHours, itemLineID)
  }
}
