package com.manaknight.app.ui.fragments

import Manaknight.R
import androidx.navigation.ActionOnlyNavDirections
import androidx.navigation.NavDirections

public class LoginFragmentDirections private constructor() {
  public companion object {
    public fun actionLoginFragmentToHomeFragment(): NavDirections =
        ActionOnlyNavDirections(R.id.action_loginFragment_to_homeFragment)

    public fun actionLoginFragmentToSignUpFragment(): NavDirections =
        ActionOnlyNavDirections(R.id.action_loginFragment_to_signUpFragment)

    public fun actionLoginFragmentToForgetPasswordFragment(): NavDirections =
        ActionOnlyNavDirections(R.id.action_loginFragment_to_forgetPasswordFragment)

    public fun actionLoginFragmentToSubscriptionFragment(): NavDirections =
        ActionOnlyNavDirections(R.id.action_loginFragment_to_subscriptionFragment)
  }
}
