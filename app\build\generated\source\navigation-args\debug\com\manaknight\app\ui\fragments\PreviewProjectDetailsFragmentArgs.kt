package com.manaknight.app.ui.fragments

import android.os.Bundle
import androidx.lifecycle.SavedStateHandle
import androidx.navigation.NavArgs
import java.lang.IllegalArgumentException
import kotlin.Int
import kotlin.jvm.JvmStatic

public data class PreviewProjectDetailsFragmentArgs(
  public val projectID: Int = 0
) : NavArgs {
  public fun toBundle(): Bundle {
    val result = Bundle()
    result.putInt("projectID", this.projectID)
    return result
  }

  public fun toSavedStateHandle(): SavedStateHandle {
    val result = SavedStateHandle()
    result.set("projectID", this.projectID)
    return result
  }

  public companion object {
    @JvmStatic
    public fun fromBundle(bundle: Bundle): PreviewProjectDetailsFragmentArgs {
      bundle.setClassLoader(PreviewProjectDetailsFragmentArgs::class.java.classLoader)
      val __projectID : Int
      if (bundle.containsKey("projectID")) {
        __projectID = bundle.getInt("projectID")
      } else {
        __projectID = 0
      }
      return PreviewProjectDetailsFragmentArgs(__projectID)
    }

    @JvmStatic
    public fun fromSavedStateHandle(savedStateHandle: SavedStateHandle):
        PreviewProjectDetailsFragmentArgs {
      val __projectID : Int?
      if (savedStateHandle.contains("projectID")) {
        __projectID = savedStateHandle["projectID"]
        if (__projectID == null) {
          throw IllegalArgumentException("Argument \"projectID\" of type integer does not support null values")
        }
      } else {
        __projectID = 0
      }
      return PreviewProjectDetailsFragmentArgs(__projectID)
    }
  }
}
