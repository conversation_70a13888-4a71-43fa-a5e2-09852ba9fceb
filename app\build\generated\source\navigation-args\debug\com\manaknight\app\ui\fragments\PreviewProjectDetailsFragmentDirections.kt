package com.manaknight.app.ui.fragments

import Manaknight.R
import android.os.Bundle
import androidx.navigation.NavDirections
import kotlin.Int
import kotlin.String

public class PreviewProjectDetailsFragmentDirections private constructor() {
  private data class ActionPreviewProjectDetailsFragmentToInvoiceFragment(
    public val projectID: Int = 0
  ) : NavDirections {
    public override val actionId: Int = R.id.action_previewProjectDetailsFragment_to_InvoiceFragment

    public override val arguments: Bundle
      get() {
        val result = Bundle()
        result.putInt("projectID", this.projectID)
        return result
      }
  }

  private data class ActionPreviewProjectDetailsFragmentToLineItemView(
    public val projectID: Int = 0,
    public val customerName: String = ""
  ) : NavDirections {
    public override val actionId: Int = R.id.action_previewProjectDetailsFragment_to_lineItemView

    public override val arguments: Bundle
      get() {
        val result = Bundle()
        result.putInt("projectID", this.projectID)
        result.putString("customerName", this.customerName)
        return result
      }
  }

  public companion object {
    public fun actionPreviewProjectDetailsFragmentToInvoiceFragment(projectID: Int = 0):
        NavDirections = ActionPreviewProjectDetailsFragmentToInvoiceFragment(projectID)

    public fun actionPreviewProjectDetailsFragmentToLineItemView(projectID: Int = 0,
        customerName: String = ""): NavDirections =
        ActionPreviewProjectDetailsFragmentToLineItemView(projectID, customerName)
  }
}
