package com.manaknight.app.ui.fragments

import Manaknight.R
import androidx.navigation.ActionOnlyNavDirections
import androidx.navigation.NavDirections

public class ProfileEditFragmentDirections private constructor() {
  public companion object {
    public fun actionProfileEditFragmentToHomeFragment(): NavDirections =
        ActionOnlyNavDirections(R.id.action_profileEditFragment_to_homeFragment)

    public fun actionProfileEditFragmentToLoginFragment(): NavDirections =
        ActionOnlyNavDirections(R.id.action_profileEditFragment_to_loginFragment)
  }
}
