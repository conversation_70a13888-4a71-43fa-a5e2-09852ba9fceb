package com.manaknight.app.ui.fragments

import Manaknight.R
import android.os.Bundle
import androidx.navigation.NavDirections
import kotlin.Int
import kotlin.String

public class ProjectDetailsFragmentDirections private constructor() {
  private data class ActionProjectDetailsFragmentToProjectTrackingFragment(
    public val projectId: Int = 0,
    public val customerName: String = ""
  ) : NavDirections {
    public override val actionId: Int =
        R.id.action_projectDetailsFragment_to_ProjectTrackingFragment

    public override val arguments: Bundle
      get() {
        val result = Bundle()
        result.putInt("projectId", this.projectId)
        result.putString("customerName", this.customerName)
        return result
      }
  }

  private data class ActionProjectDetailsFragmentToProjectChangeOrderFragment(
    public val projectID: Int = 0,
    public val customerName: String = ""
  ) : NavDirections {
    public override val actionId: Int =
        R.id.action_projectDetailsFragment_to_projectChangeOrderFragment

    public override val arguments: Bundle
      get() {
        val result = Bundle()
        result.putInt("projectID", this.projectID)
        result.putString("customerName", this.customerName)
        return result
      }
  }

  public companion object {
    public fun actionProjectDetailsFragmentToProjectTrackingFragment(projectId: Int = 0,
        customerName: String = ""): NavDirections =
        ActionProjectDetailsFragmentToProjectTrackingFragment(projectId, customerName)

    public fun actionProjectDetailsFragmentToProjectChangeOrderFragment(projectID: Int = 0,
        customerName: String = ""): NavDirections =
        ActionProjectDetailsFragmentToProjectChangeOrderFragment(projectID, customerName)
  }
}
