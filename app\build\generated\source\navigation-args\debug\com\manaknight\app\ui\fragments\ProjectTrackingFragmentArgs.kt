package com.manaknight.app.ui.fragments

import android.os.Bundle
import androidx.lifecycle.SavedStateHandle
import androidx.navigation.NavArgs
import java.lang.IllegalArgumentException
import kotlin.Int
import kotlin.String
import kotlin.jvm.JvmStatic

public data class ProjectTrackingFragmentArgs(
  public val projectId: Int = 0,
  public val customerName: String = ""
) : NavArgs {
  public fun toBundle(): Bundle {
    val result = Bundle()
    result.putInt("projectId", this.projectId)
    result.putString("customerName", this.customerName)
    return result
  }

  public fun toSavedStateHandle(): SavedStateHandle {
    val result = SavedStateHandle()
    result.set("projectId", this.projectId)
    result.set("customerName", this.customerName)
    return result
  }

  public companion object {
    @JvmStatic
    public fun fromBundle(bundle: Bundle): ProjectTrackingFragmentArgs {
      bundle.setClassLoader(ProjectTrackingFragmentArgs::class.java.classLoader)
      val __projectId : Int
      if (bundle.containsKey("projectId")) {
        __projectId = bundle.getInt("projectId")
      } else {
        __projectId = 0
      }
      val __customerName : String?
      if (bundle.containsKey("customerName")) {
        __customerName = bundle.getString("customerName")
        if (__customerName == null) {
          throw IllegalArgumentException("Argument \"customerName\" is marked as non-null but was passed a null value.")
        }
      } else {
        __customerName = ""
      }
      return ProjectTrackingFragmentArgs(__projectId, __customerName)
    }

    @JvmStatic
    public fun fromSavedStateHandle(savedStateHandle: SavedStateHandle):
        ProjectTrackingFragmentArgs {
      val __projectId : Int?
      if (savedStateHandle.contains("projectId")) {
        __projectId = savedStateHandle["projectId"]
        if (__projectId == null) {
          throw IllegalArgumentException("Argument \"projectId\" of type integer does not support null values")
        }
      } else {
        __projectId = 0
      }
      val __customerName : String?
      if (savedStateHandle.contains("customerName")) {
        __customerName = savedStateHandle["customerName"]
        if (__customerName == null) {
          throw IllegalArgumentException("Argument \"customerName\" is marked as non-null but was passed a null value")
        }
      } else {
        __customerName = ""
      }
      return ProjectTrackingFragmentArgs(__projectId, __customerName)
    }
  }
}
