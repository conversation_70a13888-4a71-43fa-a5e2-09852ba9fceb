package com.manaknight.app.ui.fragments

import Manaknight.R
import androidx.navigation.ActionOnlyNavDirections
import androidx.navigation.NavDirections

public class SignUpFragmentDirections private constructor() {
  public companion object {
    public fun actionSignUpFragmentToHomeFragment(): NavDirections =
        ActionOnlyNavDirections(R.id.action_signUpFragment_to_homeFragment)

    public fun actionSignUpFragmentToLoginFragment(): NavDirections =
        ActionOnlyNavDirections(R.id.action_signUpFragment_to_loginFragment)

    public fun actionSignUpFragmentToSubscriptionFragment(): NavDirections =
        ActionOnlyNavDirections(R.id.action_signUpFragment_to_subscriptionFragment)
  }
}
