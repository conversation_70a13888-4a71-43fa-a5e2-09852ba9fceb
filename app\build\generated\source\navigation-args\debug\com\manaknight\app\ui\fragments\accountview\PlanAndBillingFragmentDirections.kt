package com.manaknight.app.ui.fragments.accountview

import Manaknight.R
import androidx.navigation.ActionOnlyNavDirections
import androidx.navigation.NavDirections

public class PlanAndBillingFragmentDirections private constructor() {
  public companion object {
    public fun actionPlanAndBillingFragmentToPaymentsFragment(): NavDirections =
        ActionOnlyNavDirections(R.id.action_planAndBillingFragment_to_paymentsFragment)

    public fun actionPlanAndBillingFragmentToFinalCancelFragment(): NavDirections =
        ActionOnlyNavDirections(R.id.action_planAndBillingFragment_to_finalCancelFragment)

    public fun actionPlanAndBillingFragmentToSubscriptionTestFragment(): NavDirections =
        ActionOnlyNavDirections(R.id.action_planAndBillingFragment_to_subscriptionTestFragment)
  }
}
