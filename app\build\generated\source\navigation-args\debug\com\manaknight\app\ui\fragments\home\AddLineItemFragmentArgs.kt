package com.manaknight.app.ui.fragments.home

import android.os.Bundle
import androidx.lifecycle.SavedStateHandle
import androidx.navigation.NavArgs
import java.lang.IllegalArgumentException
import kotlin.Int
import kotlin.jvm.JvmStatic

public data class AddLineItemFragmentArgs(
  public val projectID: Int = 0,
  public val lineItem: Int = 0
) : NavArgs {
  public fun toBundle(): Bundle {
    val result = Bundle()
    result.putInt("projectID", this.projectID)
    result.putInt("lineItem", this.lineItem)
    return result
  }

  public fun toSavedStateHandle(): SavedStateHandle {
    val result = SavedStateHandle()
    result.set("projectID", this.projectID)
    result.set("lineItem", this.lineItem)
    return result
  }

  public companion object {
    @JvmStatic
    public fun fromBundle(bundle: Bundle): AddLineItemFragmentArgs {
      bundle.setClassLoader(AddLineItemFragmentArgs::class.java.classLoader)
      val __projectID : Int
      if (bundle.containsKey("projectID")) {
        __projectID = bundle.getInt("projectID")
      } else {
        __projectID = 0
      }
      val __lineItem : Int
      if (bundle.containsKey("lineItem")) {
        __lineItem = bundle.getInt("lineItem")
      } else {
        __lineItem = 0
      }
      return AddLineItemFragmentArgs(__projectID, __lineItem)
    }

    @JvmStatic
    public fun fromSavedStateHandle(savedStateHandle: SavedStateHandle): AddLineItemFragmentArgs {
      val __projectID : Int?
      if (savedStateHandle.contains("projectID")) {
        __projectID = savedStateHandle["projectID"]
        if (__projectID == null) {
          throw IllegalArgumentException("Argument \"projectID\" of type integer does not support null values")
        }
      } else {
        __projectID = 0
      }
      val __lineItem : Int?
      if (savedStateHandle.contains("lineItem")) {
        __lineItem = savedStateHandle["lineItem"]
        if (__lineItem == null) {
          throw IllegalArgumentException("Argument \"lineItem\" of type integer does not support null values")
        }
      } else {
        __lineItem = 0
      }
      return AddLineItemFragmentArgs(__projectID, __lineItem)
    }
  }
}
