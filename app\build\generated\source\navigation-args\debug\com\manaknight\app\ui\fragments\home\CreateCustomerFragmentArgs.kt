package com.manaknight.app.ui.fragments.home

import android.os.Bundle
import androidx.lifecycle.SavedStateHandle
import androidx.navigation.NavArgs
import java.lang.IllegalArgumentException
import kotlin.Int
import kotlin.String
import kotlin.jvm.JvmStatic

public data class CreateCustomerFragmentArgs(
  public val customerID: Int = 0,
  public val customerName: String = "",
  public val customerAddress: String = "",
  public val customerEmail: String = "",
  public val customerPhoneNumber: String = ""
) : NavArgs {
  public fun toBundle(): Bundle {
    val result = Bundle()
    result.putInt("customerID", this.customerID)
    result.putString("customerName", this.customerName)
    result.putString("customerAddress", this.customerAddress)
    result.putString("customerEmail", this.customerEmail)
    result.putString("customerPhoneNumber", this.customerPhoneNumber)
    return result
  }

  public fun toSavedStateHandle(): SavedStateHandle {
    val result = SavedStateHandle()
    result.set("customerID", this.customerID)
    result.set("customerName", this.customerName)
    result.set("customerAddress", this.customerAddress)
    result.set("customerEmail", this.customerEmail)
    result.set("customerPhoneNumber", this.customerPhoneNumber)
    return result
  }

  public companion object {
    @JvmStatic
    public fun fromBundle(bundle: Bundle): CreateCustomerFragmentArgs {
      bundle.setClassLoader(CreateCustomerFragmentArgs::class.java.classLoader)
      val __customerID : Int
      if (bundle.containsKey("customerID")) {
        __customerID = bundle.getInt("customerID")
      } else {
        __customerID = 0
      }
      val __customerName : String?
      if (bundle.containsKey("customerName")) {
        __customerName = bundle.getString("customerName")
        if (__customerName == null) {
          throw IllegalArgumentException("Argument \"customerName\" is marked as non-null but was passed a null value.")
        }
      } else {
        __customerName = ""
      }
      val __customerAddress : String?
      if (bundle.containsKey("customerAddress")) {
        __customerAddress = bundle.getString("customerAddress")
        if (__customerAddress == null) {
          throw IllegalArgumentException("Argument \"customerAddress\" is marked as non-null but was passed a null value.")
        }
      } else {
        __customerAddress = ""
      }
      val __customerEmail : String?
      if (bundle.containsKey("customerEmail")) {
        __customerEmail = bundle.getString("customerEmail")
        if (__customerEmail == null) {
          throw IllegalArgumentException("Argument \"customerEmail\" is marked as non-null but was passed a null value.")
        }
      } else {
        __customerEmail = ""
      }
      val __customerPhoneNumber : String?
      if (bundle.containsKey("customerPhoneNumber")) {
        __customerPhoneNumber = bundle.getString("customerPhoneNumber")
        if (__customerPhoneNumber == null) {
          throw IllegalArgumentException("Argument \"customerPhoneNumber\" is marked as non-null but was passed a null value.")
        }
      } else {
        __customerPhoneNumber = ""
      }
      return CreateCustomerFragmentArgs(__customerID, __customerName, __customerAddress,
          __customerEmail, __customerPhoneNumber)
    }

    @JvmStatic
    public fun fromSavedStateHandle(savedStateHandle: SavedStateHandle):
        CreateCustomerFragmentArgs {
      val __customerID : Int?
      if (savedStateHandle.contains("customerID")) {
        __customerID = savedStateHandle["customerID"]
        if (__customerID == null) {
          throw IllegalArgumentException("Argument \"customerID\" of type integer does not support null values")
        }
      } else {
        __customerID = 0
      }
      val __customerName : String?
      if (savedStateHandle.contains("customerName")) {
        __customerName = savedStateHandle["customerName"]
        if (__customerName == null) {
          throw IllegalArgumentException("Argument \"customerName\" is marked as non-null but was passed a null value")
        }
      } else {
        __customerName = ""
      }
      val __customerAddress : String?
      if (savedStateHandle.contains("customerAddress")) {
        __customerAddress = savedStateHandle["customerAddress"]
        if (__customerAddress == null) {
          throw IllegalArgumentException("Argument \"customerAddress\" is marked as non-null but was passed a null value")
        }
      } else {
        __customerAddress = ""
      }
      val __customerEmail : String?
      if (savedStateHandle.contains("customerEmail")) {
        __customerEmail = savedStateHandle["customerEmail"]
        if (__customerEmail == null) {
          throw IllegalArgumentException("Argument \"customerEmail\" is marked as non-null but was passed a null value")
        }
      } else {
        __customerEmail = ""
      }
      val __customerPhoneNumber : String?
      if (savedStateHandle.contains("customerPhoneNumber")) {
        __customerPhoneNumber = savedStateHandle["customerPhoneNumber"]
        if (__customerPhoneNumber == null) {
          throw IllegalArgumentException("Argument \"customerPhoneNumber\" is marked as non-null but was passed a null value")
        }
      } else {
        __customerPhoneNumber = ""
      }
      return CreateCustomerFragmentArgs(__customerID, __customerName, __customerAddress,
          __customerEmail, __customerPhoneNumber)
    }
  }
}
