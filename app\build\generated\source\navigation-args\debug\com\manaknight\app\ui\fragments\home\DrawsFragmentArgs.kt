package com.manaknight.app.ui.fragments.home

import android.os.Bundle
import androidx.lifecycle.SavedStateHandle
import androidx.navigation.NavArgs
import java.lang.IllegalArgumentException
import kotlin.Int
import kotlin.String
import kotlin.jvm.JvmStatic

public data class DrawsFragmentArgs(
  public val projectID: Int = 0,
  public val customerName: String = ""
) : NavArgs {
  public fun toBundle(): Bundle {
    val result = Bundle()
    result.putInt("projectID", this.projectID)
    result.putString("customerName", this.customerName)
    return result
  }

  public fun toSavedStateHandle(): SavedStateHandle {
    val result = SavedStateHandle()
    result.set("projectID", this.projectID)
    result.set("customerName", this.customerName)
    return result
  }

  public companion object {
    @JvmStatic
    public fun fromBundle(bundle: Bundle): DrawsFragmentArgs {
      bundle.setClassLoader(DrawsFragmentArgs::class.java.classLoader)
      val __projectID : Int
      if (bundle.containsKey("projectID")) {
        __projectID = bundle.getInt("projectID")
      } else {
        __projectID = 0
      }
      val __customerName : String?
      if (bundle.containsKey("customerName")) {
        __customerName = bundle.getString("customerName")
        if (__customerName == null) {
          throw IllegalArgumentException("Argument \"customerName\" is marked as non-null but was passed a null value.")
        }
      } else {
        __customerName = ""
      }
      return DrawsFragmentArgs(__projectID, __customerName)
    }

    @JvmStatic
    public fun fromSavedStateHandle(savedStateHandle: SavedStateHandle): DrawsFragmentArgs {
      val __projectID : Int?
      if (savedStateHandle.contains("projectID")) {
        __projectID = savedStateHandle["projectID"]
        if (__projectID == null) {
          throw IllegalArgumentException("Argument \"projectID\" of type integer does not support null values")
        }
      } else {
        __projectID = 0
      }
      val __customerName : String?
      if (savedStateHandle.contains("customerName")) {
        __customerName = savedStateHandle["customerName"]
        if (__customerName == null) {
          throw IllegalArgumentException("Argument \"customerName\" is marked as non-null but was passed a null value")
        }
      } else {
        __customerName = ""
      }
      return DrawsFragmentArgs(__projectID, __customerName)
    }
  }
}
