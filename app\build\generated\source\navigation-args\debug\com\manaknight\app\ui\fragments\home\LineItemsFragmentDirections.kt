package com.manaknight.app.ui.fragments.home

import Manaknight.R
import android.os.Bundle
import android.os.Parcelable
import androidx.navigation.NavDirections
import com.manaknight.app.model.remote.profitPro.MaterialRespListModel2
import java.io.Serializable
import java.lang.UnsupportedOperationException
import kotlin.Int
import kotlin.String
import kotlin.Suppress

public class LineItemsFragmentDirections private constructor() {
  private data class ActionLineItemViewToAddlineItemView(
    public val projectID: Int = 0,
    public val lineItem: Int = 0
  ) : NavDirections {
    public override val actionId: Int = R.id.action_lineItemView_to_addlineItemView

    public override val arguments: Bundle
      get() {
        val result = Bundle()
        result.putInt("projectID", this.projectID)
        result.putInt("lineItem", this.lineItem)
        return result
      }
  }

  private data class ActionLineItemViewToDrawsDetailView(
    public val projectID: Int = 0,
    public val customerName: String = ""
  ) : NavDirections {
    public override val actionId: Int = R.id.action_lineItemView_to_drawsDetailView

    public override val arguments: Bundle
      get() {
        val result = Bundle()
        result.putInt("projectID", this.projectID)
        result.putString("customerName", this.customerName)
        return result
      }
  }

  private data class ActionLineItemViewToAddMateriallineItemView(
    public val materialItem: MaterialRespListModel2,
    public val projectID: Int = 0,
    public val lineItem: Int = 0,
    public val lineDescrition: String = "0",
    public val lineEstimateType: String = "0",
    public val isEditable: Int = 0,
    public val labourHours: String = "0",
    public val itemLineID: Int = 0
  ) : NavDirections {
    public override val actionId: Int = R.id.action_lineItemView_to_addMateriallineItemView

    public override val arguments: Bundle
      @Suppress("CAST_NEVER_SUCCEEDS")
      get() {
        val result = Bundle()
        result.putInt("projectID", this.projectID)
        result.putInt("lineItem", this.lineItem)
        result.putString("lineDescrition", this.lineDescrition)
        result.putString("lineEstimateType", this.lineEstimateType)
        result.putInt("isEditable", this.isEditable)
        result.putString("labourHours", this.labourHours)
        result.putInt("itemLineID", this.itemLineID)
        if (Parcelable::class.java.isAssignableFrom(MaterialRespListModel2::class.java)) {
          result.putParcelable("materialItem", this.materialItem as Parcelable)
        } else if (Serializable::class.java.isAssignableFrom(MaterialRespListModel2::class.java)) {
          result.putSerializable("materialItem", this.materialItem as Serializable)
        } else {
          throw UnsupportedOperationException(MaterialRespListModel2::class.java.name +
              " must implement Parcelable or Serializable or must be an Enum.")
        }
        return result
      }
  }

  private data class ActionLineItemViewToAddLinearlineItemView(
    public val materialItem: MaterialRespListModel2,
    public val projectID: Int = 0,
    public val lineItem: Int = 0,
    public val lineDescrition: String = "0",
    public val lineEstimateType: String = "0",
    public val isEditable: Int = 0,
    public val labourHours: String = "0",
    public val itemLineID: Int = 0
  ) : NavDirections {
    public override val actionId: Int = R.id.action_lineItemView_to_addLinearlineItemView

    public override val arguments: Bundle
      @Suppress("CAST_NEVER_SUCCEEDS")
      get() {
        val result = Bundle()
        result.putInt("projectID", this.projectID)
        result.putInt("lineItem", this.lineItem)
        result.putString("lineDescrition", this.lineDescrition)
        result.putString("lineEstimateType", this.lineEstimateType)
        result.putInt("isEditable", this.isEditable)
        result.putString("labourHours", this.labourHours)
        result.putInt("itemLineID", this.itemLineID)
        if (Parcelable::class.java.isAssignableFrom(MaterialRespListModel2::class.java)) {
          result.putParcelable("materialItem", this.materialItem as Parcelable)
        } else if (Serializable::class.java.isAssignableFrom(MaterialRespListModel2::class.java)) {
          result.putSerializable("materialItem", this.materialItem as Serializable)
        } else {
          throw UnsupportedOperationException(MaterialRespListModel2::class.java.name +
              " must implement Parcelable or Serializable or must be an Enum.")
        }
        return result
      }
  }

  public companion object {
    public fun actionLineItemViewToAddlineItemView(projectID: Int = 0, lineItem: Int = 0):
        NavDirections = ActionLineItemViewToAddlineItemView(projectID, lineItem)

    public fun actionLineItemViewToDrawsDetailView(projectID: Int = 0, customerName: String = ""):
        NavDirections = ActionLineItemViewToDrawsDetailView(projectID, customerName)

    public fun actionLineItemViewToAddMateriallineItemView(
      materialItem: MaterialRespListModel2,
      projectID: Int = 0,
      lineItem: Int = 0,
      lineDescrition: String = "0",
      lineEstimateType: String = "0",
      isEditable: Int = 0,
      labourHours: String = "0",
      itemLineID: Int = 0
    ): NavDirections = ActionLineItemViewToAddMateriallineItemView(materialItem, projectID,
        lineItem, lineDescrition, lineEstimateType, isEditable, labourHours, itemLineID)

    public fun actionLineItemViewToAddLinearlineItemView(
      materialItem: MaterialRespListModel2,
      projectID: Int = 0,
      lineItem: Int = 0,
      lineDescrition: String = "0",
      lineEstimateType: String = "0",
      isEditable: Int = 0,
      labourHours: String = "0",
      itemLineID: Int = 0
    ): NavDirections = ActionLineItemViewToAddLinearlineItemView(materialItem, projectID, lineItem,
        lineDescrition, lineEstimateType, isEditable, labourHours, itemLineID)
  }
}
