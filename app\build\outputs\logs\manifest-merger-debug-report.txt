-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:2:1-86:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:2:1-86:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:2:1-86:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:2:1-86:12
MERGED from [com.github.sakshampruthi:custom-loading-screen:1.16] C:\Users\<USER>\.gradle\caches\transforms-3\d596ae0a4869b18a70be323e8a610021\transformed\jetified-custom-loading-screen-1.16\AndroidManifest.xml:2:1-11:12
MERGED from [com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:2:1-32:12
MERGED from [com.stripe:stripe-3ds2-android:2.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\7f344a36bb06c1b19b3d8dc344e8ef3c\transformed\jetified-stripe-3ds2-android-2.7.2\AndroidManifest.xml:2:1-20:12
MERGED from [androidx.databinding:viewbinding:8.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d94a1d1dd722c506b8ed1d20403b573e\transformed\jetified-viewbinding-8.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\dfce37622a563a24f0c8e9b3351fbc6a\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:17:1-32:12
MERGED from [androidx.navigation:navigation-common:2.7.4] C:\Users\<USER>\.gradle\caches\transforms-3\2e728102c794137111e02ef3cc7ce58a\transformed\navigation-common-2.7.4\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-fragment:2.7.4] C:\Users\<USER>\.gradle\caches\transforms-3\d07f2507d796555fa3e9b9293e3cd644\transformed\navigation-fragment-2.7.4\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.7.4] C:\Users\<USER>\.gradle\caches\transforms-3\cafa83fd917f253b5ba51f41700f6128\transformed\navigation-runtime-2.7.4\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.4] C:\Users\<USER>\.gradle\caches\transforms-3\e27eccadf47d15b674d702d3856da599\transformed\navigation-common-ktx-2.7.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.4] C:\Users\<USER>\.gradle\caches\transforms-3\3754230fa958c0a5539f1708b5c1c207\transformed\navigation-fragment-ktx-2.7.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.4] C:\Users\<USER>\.gradle\caches\transforms-3\df7a7742007c25ef02b00676b95a8b64\transformed\navigation-runtime-ktx-2.7.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-compose:2.7.4] C:\Users\<USER>\.gradle\caches\transforms-3\01270e06c1369120c2a156bc8f0baf75\transformed\jetified-navigation-compose-2.7.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.4] C:\Users\<USER>\.gradle\caches\transforms-3\aecc66a34563bb4aa24d31833e6be989\transformed\navigation-ui-ktx-2.7.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui:2.7.4] C:\Users\<USER>\.gradle\caches\transforms-3\2d0b9e5b251e403c37690fd551fa5112\transformed\navigation-ui-2.7.4\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\1c5b7e64d37c02b896c3a005a7dfbcf9\transformed\material-1.7.0\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\2428af63cc0a3c36e460feea781f693a\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.android.libraries.places:places:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\245b102fe60f0031ab708afcac9ce0b9\transformed\jetified-places-3.0.0\AndroidManifest.xml:2:1-24:12
MERGED from [io.insert-koin:koin-android:3.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2fbb1a4b8ac61e515c75ed416a4f43b0\transformed\jetified-koin-android-3.3.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.camera:camera-view:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\2b745688d27acb49059c05228eef7303\transformed\jetified-camera-view-1.2.2\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.simonebortolin:FlowLayoutManager:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\4948fc7a26504540194a499515390cba\transformed\jetified-FlowLayoutManager-1.8.0\AndroidManifest.xml:2:1-12:12
MERGED from [com.airbnb.android:lottie:6.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\66a3728dbd899e31bbf8f1355aee8030\transformed\jetified-lottie-6.0.0\AndroidManifest.xml:2:1-11:12
MERGED from [com.tbuonomo:dotsindicator:4.3] C:\Users\<USER>\.gradle\caches\transforms-3\a910403c30b7fd7ee8d62fed1c87cccb\transformed\jetified-dotsindicator-4.3-debug\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\09ee94349df8be93cc14578888d37864\transformed\jetified-appcompat-resources-1.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\8ba2f5412e2a4ad787869c635535bb6d\transformed\appcompat-1.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f6fa1e240580d875b9faf8d016b434a\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:17:1-40:12
MERGED from [com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bca5bb2c25cf61ac91ecd47130eaf591\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:17:1-44:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\52dd77e3ac8ff42cd18cec69ec02066d\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.play:app-update-ktx:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\5cf56c4e9c2749bf1f05cde0b3175e9e\transformed\jetified-app-update-ktx-2.0.1\AndroidManifest.xml:2:1-14:12
MERGED from [com.github.bumptech.glide:glide:4.14.2] C:\Users\<USER>\.gradle\caches\transforms-3\59f9204e4302ab64a1b44acd0e33ff38\transformed\jetified-glide-4.14.2\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.android.gms:play-services-mlkit-text-recognition:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\064ab3ca430c0695fe6ffa9cfb590653\transformed\jetified-play-services-mlkit-text-recognition-18.0.2\AndroidManifest.xml:2:1-8:12
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\aeed75bc1d8b17705d6e8b075b1be815\transformed\jetified-play-services-location-21.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-fitness:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4018ae0a1966300199c2b3e05a6d81e9\transformed\jetified-play-services-fitness-20.0.0\AndroidManifest.xml:17:1-28:12
MERGED from [com.google.android.gms:play-services-vision:20.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\89198b049f9332bca64336be6116f748\transformed\jetified-play-services-vision-20.1.3\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-mlkit-text-recognition-common:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b4bd5f6b063655bfefc8564cd709cc40\transformed\jetified-play-services-mlkit-text-recognition-common-18.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.mlkit:vision-common:17.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c3691d46c450c8405e6a322b681b222a\transformed\jetified-vision-common-17.2.1\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c505a1ac40223f3f4347c0c22e87fce\transformed\jetified-common-18.5.0\AndroidManifest.xml:2:1-26:12
MERGED from [com.google.firebase:firebase-auth-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\15d4f429e740d2d9a84daaaefe71c783\transformed\jetified-firebase-auth-ktx-21.1.0\AndroidManifest.xml:2:1-17:12
MERGED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:17:1-73:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\442c19f05b5ee5d7a42222c5faaaa5f4\transformed\jetified-play-services-auth-api-phone-18.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\217712a4df42ce61eeefb0b0a4ae3b80\transformed\jetified-play-services-auth-base-18.0.4\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-fido:19.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\b1eb8bd6c0710cce723f554ae768195b\transformed\jetified-play-services-fido-19.0.1\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-vision-common:19.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\781069a29b67609796d15ed893750924\transformed\jetified-play-services-vision-common-19.1.3\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-messaging-ktx:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\c80935087eb2baf2ce8d5fda4c1695fb\transformed\jetified-firebase-messaging-ktx-23.1.1\AndroidManifest.xml:17:1-34:12
MERGED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:17:1-61:12
MERGED from [com.google.android.gms:play-services-clearcut:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\00c62090a3372c4b2e84fc74d20c66c4\transformed\jetified-play-services-clearcut-17.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-flags:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5e5492193f167549559412ad6082cce4\transformed\jetified-play-services-flags-17.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-phenotype:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5606aff7220fb34ade507a261b563053\transformed\jetified-play-services-phenotype-17.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-safetynet:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0e397d89534d8dd921d128a204dc17e5\transformed\jetified-play-services-safetynet-17.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5296fb8d536108245faa30c6d8197de1\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.android.play:app-update:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\9c5637f4640a23ae750c2105a3feb6df\transformed\jetified-app-update-2.0.1\AndroidManifest.xml:2:1-14:12
MERGED from [com.google.firebase:firebase-crashlytics-ktx:18.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\7a08c1f471d4aed10f5a62d4bdfd7cc0\transformed\jetified-firebase-crashlytics-ktx-18.3.5\AndroidManifest.xml:15:1-32:12
MERGED from [com.google.firebase:firebase-analytics-ktx:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d0398be7ec17550451ee5b14d880cfc\transformed\jetified-firebase-analytics-ktx-21.2.0\AndroidManifest.xml:2:1-17:12
MERGED from [com.google.firebase:firebase-common-ktx:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b2a903c9b0acab1e7698691d1a2c986\transformed\jetified-firebase-common-ktx-20.3.0\AndroidManifest.xml:2:1-20:12
MERGED from [com.google.firebase:firebase-crashlytics:18.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\967d8355743d170a894a29b4b1a80382\transformed\jetified-firebase-crashlytics-18.3.5\AndroidManifest.xml:2:1-23:12
MERGED from [com.google.firebase:firebase-analytics:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7ad3c7c712cc6d61b042196a1cabbfd5\transformed\jetified-firebase-analytics-21.2.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\168037942f327435dcf9fbba3d0dde22\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:17:1-37:12
MERGED from [com.google.firebase:firebase-installations:17.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\f175c9d0e7eca696c235389c9c14325d\transformed\jetified-firebase-installations-17.1.2\AndroidManifest.xml:2:1-23:12
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\36640c11b54047d7650312d271b9d737\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\b6cf1c5e33857039c8761daab9336c00\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:15:1-33:12
MERGED from [com.google.firebase:firebase-common:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\422e0883ce3188039e67d46c46566547\transformed\jetified-firebase-common-20.3.0\AndroidManifest.xml:15:1-39:12
MERGED from [com.google.mlkit:vision-interfaces:16.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5fe3db25fce9db029f09aa0094f8691c\transformed\jetified-vision-interfaces-16.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\195a69fff94f2bfb0d0e8bddaecb08d4\transformed\jetified-firebase-installations-interop-17.1.0\AndroidManifest.xml:15:1-22:12
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\4d427ed86a6d184dccce4dcab3eba6c4\transformed\jetified-play-services-cloud-messaging-17.0.1\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d1c4513013d397eaa0fbbd356cbef4a\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.accompanist:accompanist-systemuicontroller:0.33.0-alpha] C:\Users\<USER>\.gradle\caches\transforms-3\b5d0ce2e99c1e6f542db90966d336f81\transformed\jetified-accompanist-systemuicontroller-0.33.0-alpha\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.camera:camera-extensions:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\54b9491a3c7a79413b727cd19f2cf1e8\transformed\jetified-camera-extensions-1.2.2\AndroidManifest.xml:17:1-34:12
MERGED from [androidx.camera:camera-camera2:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\ee28ca93c07bf81d90ff8f6019728918\transformed\jetified-camera-camera2-1.2.2\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-lifecycle:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\11cda86714253a9d3d26b6bb06cc5cac\transformed\jetified-camera-lifecycle-1.2.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.camera:camera-core:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2f30601676b1559d6170091243eee16\transformed\jetified-camera-core-1.2.2\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9707284c2df373486e3d74a080fb4e6d\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dacd3ba96c55d27662761824276657e5\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:17:1-46:12
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\80572253c32e71649c1c4bf8e27add88\transformed\jetified-play-services-measurement-sdk-21.2.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-measurement-impl:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ebc0a470e1cbd9a5ad219bc5fd5997fd\transformed\jetified-play-services-measurement-impl-21.2.0\AndroidManifest.xml:17:1-32:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\daa3e843a443636df76a26d3b0bcf1e7\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\eb45b79d35ab87f67935e779c9e8bdf3\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bbc1907e82227ca035f4be4cfe067de0\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\32135bc568b9cde0c2f5e949dfb59663\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material3:material3-window-size-class-android:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\7a6728e66b15c37af970a20ee5332265\transformed\jetified-material3-window-size-class-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material3:material3-android:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\b287a1edc0bc12e0df878b4dbbb26e19\transformed\jetified-material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\05b1a8e3042ec632090b4eb5f8137b2b\transformed\jetified-material-icons-extended-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\ac02a7a94422dcce40cdd6c0000e16e2\transformed\jetified-material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\81cba43246bfeb52a6860a7c49256468\transformed\jetified-material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\96e1d1cc1146c2c08c7d053927fc0308\transformed\jetified-runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\070bf93e2411a88dafbd5c4c7942de93\transformed\jetified-ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\b75197b9c9125fc0157db5bd697ca3e3\transformed\jetified-ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\3b3d207575e904ff44cfc28f4120b438\transformed\jetified-ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-unit-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\cbde9d70a79d9e24cfe6ac77c925900a\transformed\jetified-ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\1bd64310f22a0763bf1c9e96e8b4eb71\transformed\jetified-runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-livedata:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\082736c6186f577f1bd1ab12faf6fe90\transformed\jetified-runtime-livedata-1.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\38f6938632726e453470b901b774bc28\transformed\jetified-ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\962c984fdf6b039b6ea374cccc04d637\transformed\jetified-ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\b13e36d21503c40a51f9490c0fb5ce2b\transformed\jetified-ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\70bc9aa5687d756269064d6bb99120e2\transformed\jetified-ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\549ede32db9c3acb303de4fa6d2a7dfa\transformed\jetified-ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.material:material-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\c6d725e0e08b97655e308af475197105\transformed\jetified-material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\a1380ac7d4a61052416a4d1b59c9b0c8\transformed\jetified-animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ce0b2e218c7b2a09736555bcc0b4970\transformed\jetified-animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\f1f79afb44259c4de9e26d81ad9b5d15\transformed\jetified-foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\344cacb528c3e1b11175f0c06e0c1359\transformed\jetified-foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\0d25d57bf2baff79cf0e8eba55181641\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\7ada1d27f1fad82fa455b667be34e866\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\078811cffc78e28bc7870a96ed097257\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\6126ffc5a4df2a2abdf3423b0d8904c9\transformed\jetified-lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\12a373fb55a564c3fe263b3f6935d1db\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\7eb0be57541310c55aab8a6fe7654e38\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\aaa1b22d2b09aff067ec5daf6e488cfc\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\d8f7ad7e9cfa4852fc12065d57ca4a88\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\e0254ede74c9b8f5b64860fd23de97f2\transformed\jetified-lifecycle-viewmodel-compose-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\d785d3b79612799668071fcb2ce91caa\transformed\jetified-lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\612da429bcd6dcfc9d3c4f1af6780bf5\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b9da2439d13dda14b327832efc078fdd\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\af559310e2286e826f29fc113b7f4a2d\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f4f6aeeb3956b84a487304df665695ac\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\ba08e4d148ac61b853fdf970301f70c9\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c45c3759094c2057e53f99bee2adf071\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ae1a62bfefe7342250403252aeb82538\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\343cb2f92f12afa4514143982741cd1c\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\e3103d9432406b321a1e2e5913be8ed6\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\ee0de7eb3f8ea765c583f4b74e820d66\transformed\browser-1.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\558ddb4f0e812e19484bb84d65c0d00f\transformed\media-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\cfa1c7e923fa1317d07bdcd9bd03777b\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\415c161ff15d16996587df02a2a7fdc1\transformed\jetified-window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2765a7455ef68c71a1c9e0db21aec1d5\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\788efde5a45f4c30ec0022beed46df83\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\513b951cb5326af6c4cb747421bddf5c\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5bfca2f7df9cffd8adb370df524988b5\transformed\jetified-core-ktx-1.12.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6aa5aa5c226d738ac0b7be23550f05e5\transformed\jetified-autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4b7543783d7e597efaf2f46cf5f3847\transformed\core-1.12.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\47aa295ad4f008a6ea3b1b56357692d1\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\1094bff9c5f3a27eb5dd6ca68de079ae\transformed\jetified-lifecycle-livedata-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\5923f21a1bf8cac115a6d7f3a4dbdf7b\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\bea17e79b9eb17c5975c225574f93648\transformed\jetified-lifecycle-runtime-compose-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\c21ba8f66cefcda1f910a4d64ece4228\transformed\jetified-lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [com.android.billingclient:billing-ktx:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\f944db2fccf568f9832b5ab32b83578e\transformed\jetified-billing-ktx-6.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\5894d67a40de79c92f240f18414ffdf3\transformed\jetified-play-services-tasks-18.0.2\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d41c20a44acb8d35f05eb6f2f49b06ae\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4471cce94b29aeb47706814ba2b6c808\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\195d9f9b0cb87448c1e03b6bfc41a24b\transformed\jetified-play-services-measurement-sdk-api-21.2.0\AndroidManifest.xml:17:1-28:12
MERGED from [com.google.android.gms:play-services-measurement-base:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\44ab81bc213b7d1a040dcf2f8680989d\transformed\jetified-play-services-measurement-base-21.2.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\3eddf5b8a9d33eb11d1830cb5e4fd1aa\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\da507c8fa600d92c1c1664b80a52110b\transformed\fragment-1.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment-ktx:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\6d349a96672a9169633f047b27fe21ae\transformed\jetified-fragment-ktx-1.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d541a9cc09c6ffbb54db4c38bfa679b\transformed\jetified-activity-ktx-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\273932cfa347fd030d8f74b6c30f902e\transformed\jetified-activity-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-compose:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\30556913ce86c30d62df9c84fa8696d5\transformed\jetified-activity-compose-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-splashscreen:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0b38a6f884d3b2956d400ad9386058b6\transformed\jetified-core-splashscreen-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\8ce63201cb3fc8f8a03db0835f910fc5\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\0d2b250ca7a066e4f86c5139c779b394\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [com.github.PhilJay:MPAndroidChart:v3.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7303e81fbffacbcbcb90a66cd126534f\transformed\jetified-MPAndroidChart-v3.1.0\AndroidManifest.xml:2:1-20:12
MERGED from [com.facebook.shimmer:shimmer:0.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\0f679c305182e03100d64ad422788b57\transformed\jetified-shimmer-0.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4143901f208cdfa6ffbcfacd2d0a9d1\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3dcdef5b9f04aee58a418e24c6945f8\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.14.2] C:\Users\<USER>\.gradle\caches\transforms-3\d97c3db64fd63c39af8a8f807d4f9588\transformed\jetified-gifdecoder-4.14.2\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.exifinterface:exifinterface:1.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\0c5fef66de8c362a273c2a6af5439677\transformed\exifinterface-1.3.3\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\c900719aaf192cd5689ec37a91c858c8\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c2d2042dcaf0377e763c5906335d1b53\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\237161b9ad839da30c99f15645867c90\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-components:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\80f9112ff9b4f6703f8e86c4a16dca98\transformed\jetified-firebase-components-17.1.0\AndroidManifest.xml:15:1-22:12
MERGED from [com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\23890166d6a8b14dee3091bd6ffdea5c\transformed\jetified-billing-6.0.1\AndroidManifest.xml:2:1-30:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e0f5c7c79df0748d229f2beda36ab74d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:15:1-37:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e3b38192b92eeb3fc9e8187da50ce6a9\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b28104446ff0f3d3f5e4b1c7861cccc0\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:15:1-23:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\06bbd20a458f343f77beb3bb8e73f53d\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b2cfc7cca7cf941fb5a48ea556764a5\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.datatransport:transport-api:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2090c19d7b5dec67bde921689114a109\transformed\jetified-transport-api-3.0.0\AndroidManifest.xml:15:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\13dad629b9d828476a8b326a2ac54af6\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\86d41ed1b81d8bd9d2260149f60eb3e3\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.maps.android:android-maps-utils:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d812f540c2906d43adfa5531b198632c\transformed\jetified-android-maps-utils-2.2.0\AndroidManifest.xml:2:1-17:12
MERGED from [com.github.duanhong169:colorpicker:1.1.6] C:\Users\<USER>\.gradle\caches\transforms-3\9a1d3a692c53e362f30caa998387687c\transformed\jetified-colorpicker-1.1.6\AndroidManifest.xml:2:1-11:12
MERGED from [com.github.duanhong169:checkerboarddrawable:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\2b842736fcb536cdd2b6459f437e2379\transformed\jetified-checkerboarddrawable-1.0.2\AndroidManifest.xml:2:1-11:12
MERGED from [com.intuit.sdp:sdp-android:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c1298cb649060719aeb7e0babf1b9f7c\transformed\jetified-sdp-android-1.1.0\AndroidManifest.xml:2:1-11:12
MERGED from [com.intuit.ssp:ssp-android:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\fe1f3fa813fd8988796c41e4c70b5f0f\transformed\jetified-ssp-android-1.1.0\AndroidManifest.xml:2:1-11:12
MERGED from [pl.droidsonroids.gif:android-gif-drawable:1.2.22] C:\Users\<USER>\.gradle\caches\transforms-3\92466a5cfd4dfe9238e8cc1f003a2347\transformed\jetified-android-gif-drawable-1.2.22\AndroidManifest.xml:2:1-9:12
MERGED from [com.android.volley:volley:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\d2aa71aa11f556cb890e82e42e162595\transformed\jetified-volley-1.2.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.play:core-common:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\dc72e552015300076bb41fda69eedbd2\transformed\jetified-core-common-2.0.2\AndroidManifest.xml:2:1-21:12
MERGED from [pl.droidsonroids.relinker:relinker:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\0d00c90549ca0083eb4a8142f3073b8b\transformed\jetified-relinker-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\transforms-3\d232e3111dc78fa8c6f2ad38a2151a17\transformed\jetified-image-1.0.0-beta1\AndroidManifest.xml:2:1-9:12
	package
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:6:5-76
MERGED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:25:5-77
MERGED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:25:5-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:6:22-74
uses-permission#android.permission.VIBRATE
ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:7:5-66
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:7:22-63
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.libraries.places:places:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\245b102fe60f0031ab708afcac9ce0b9\transformed\jetified-places-3.0.0\AndroidManifest.xml:12:5-67
MERGED from [com.google.android.libraries.places:places:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\245b102fe60f0031ab708afcac9ce0b9\transformed\jetified-places-3.0.0\AndroidManifest.xml:12:5-67
MERGED from [com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bca5bb2c25cf61ac91ecd47130eaf591\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bca5bb2c25cf61ac91ecd47130eaf591\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.firebase:firebase-crashlytics:18.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\967d8355743d170a894a29b4b1a80382\transformed\jetified-firebase-crashlytics-18.3.5\AndroidManifest.xml:9:5-67
MERGED from [com.google.firebase:firebase-crashlytics:18.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\967d8355743d170a894a29b4b1a80382\transformed\jetified-firebase-crashlytics-18.3.5\AndroidManifest.xml:9:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\168037942f327435dcf9fbba3d0dde22\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\168037942f327435dcf9fbba3d0dde22\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.firebase:firebase-installations:17.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\f175c9d0e7eca696c235389c9c14325d\transformed\jetified-firebase-installations-17.1.2\AndroidManifest.xml:10:5-67
MERGED from [com.google.firebase:firebase-installations:17.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\f175c9d0e7eca696c235389c9c14325d\transformed\jetified-firebase-installations-17.1.2\AndroidManifest.xml:10:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\4d427ed86a6d184dccce4dcab3eba6c4\transformed\jetified-play-services-cloud-messaging-17.0.1\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\4d427ed86a6d184dccce4dcab3eba6c4\transformed\jetified-play-services-cloud-messaging-17.0.1\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ebc0a470e1cbd9a5ad219bc5fd5997fd\transformed\jetified-play-services-measurement-impl-21.2.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ebc0a470e1cbd9a5ad219bc5fd5997fd\transformed\jetified-play-services-measurement-impl-21.2.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\195d9f9b0cb87448c1e03b6bfc41a24b\transformed\jetified-play-services-measurement-sdk-api-21.2.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\195d9f9b0cb87448c1e03b6bfc41a24b\transformed\jetified-play-services-measurement-sdk-api-21.2.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e0f5c7c79df0748d229f2beda36ab74d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e0f5c7c79df0748d229f2beda36ab74d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:25:5-67
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:8:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:9:5-79
MERGED from [com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bca5bb2c25cf61ac91ecd47130eaf591\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bca5bb2c25cf61ac91ecd47130eaf591\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\168037942f327435dcf9fbba3d0dde22\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\168037942f327435dcf9fbba3d0dde22\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-installations:17.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\f175c9d0e7eca696c235389c9c14325d\transformed\jetified-firebase-installations-17.1.2\AndroidManifest.xml:9:5-79
MERGED from [com.google.firebase:firebase-installations:17.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\f175c9d0e7eca696c235389c9c14325d\transformed\jetified-firebase-installations-17.1.2\AndroidManifest.xml:9:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\4d427ed86a6d184dccce4dcab3eba6c4\transformed\jetified-play-services-cloud-messaging-17.0.1\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\4d427ed86a6d184dccce4dcab3eba6c4\transformed\jetified-play-services-cloud-messaging-17.0.1\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ebc0a470e1cbd9a5ad219bc5fd5997fd\transformed\jetified-play-services-measurement-impl-21.2.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ebc0a470e1cbd9a5ad219bc5fd5997fd\transformed\jetified-play-services-measurement-impl-21.2.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\195d9f9b0cb87448c1e03b6bfc41a24b\transformed\jetified-play-services-measurement-sdk-api-21.2.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\195d9f9b0cb87448c1e03b6bfc41a24b\transformed\jetified-play-services-measurement-sdk-api-21.2.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e0f5c7c79df0748d229f2beda36ab74d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e0f5c7c79df0748d229f2beda36ab74d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e3b38192b92eeb3fc9e8187da50ce6a9\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e3b38192b92eeb3fc9e8187da50ce6a9\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:22:5-79
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:9:22-76
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:10:5-79
MERGED from [com.google.android.libraries.places:places:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\245b102fe60f0031ab708afcac9ce0b9\transformed\jetified-places-3.0.0\AndroidManifest.xml:10:5-79
MERGED from [com.google.android.libraries.places:places:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\245b102fe60f0031ab708afcac9ce0b9\transformed\jetified-places-3.0.0\AndroidManifest.xml:10:5-79
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:10:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:11:5-81
MERGED from [com.google.android.libraries.places:places:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\245b102fe60f0031ab708afcac9ce0b9\transformed\jetified-places-3.0.0\AndroidManifest.xml:9:5-81
MERGED from [com.google.android.libraries.places:places:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\245b102fe60f0031ab708afcac9ce0b9\transformed\jetified-places-3.0.0\AndroidManifest.xml:9:5-81
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:11:22-78
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:12:5-76
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:12:22-73
uses-permission#android.permission.CAMERA
ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:13:5-65
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:13:22-62
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:14:5-80
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:14:22-78
uses-permission#android.permission.ACTIVITY_RECOGNITION
ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:15:5-79
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:15:22-76
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:16:5-68
MERGED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:26:5-68
MERGED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:26:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\168037942f327435dcf9fbba3d0dde22\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\168037942f327435dcf9fbba3d0dde22\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\4d427ed86a6d184dccce4dcab3eba6c4\transformed\jetified-play-services-cloud-messaging-17.0.1\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\4d427ed86a6d184dccce4dcab3eba6c4\transformed\jetified-play-services-cloud-messaging-17.0.1\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ebc0a470e1cbd9a5ad219bc5fd5997fd\transformed\jetified-play-services-measurement-impl-21.2.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ebc0a470e1cbd9a5ad219bc5fd5997fd\transformed\jetified-play-services-measurement-impl-21.2.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\195d9f9b0cb87448c1e03b6bfc41a24b\transformed\jetified-play-services-measurement-sdk-api-21.2.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\195d9f9b0cb87448c1e03b6bfc41a24b\transformed\jetified-play-services-measurement-sdk-api-21.2.0\AndroidManifest.xml:25:5-68
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:16:22-65
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:18:5-81
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:18:22-78
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:19:5-80
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:19:22-77
uses-permission#android.permission.MANAGE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:20:5-81
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:20:22-79
uses-permission#com.android.vending.BILLING
ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:21:5-67
MERGED from [com.android.billingclient:billing-ktx:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\f944db2fccf568f9832b5ab32b83578e\transformed\jetified-billing-ktx-6.0.1\AndroidManifest.xml:9:5-67
MERGED from [com.android.billingclient:billing-ktx:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\f944db2fccf568f9832b5ab32b83578e\transformed\jetified-billing-ktx-6.0.1\AndroidManifest.xml:9:5-67
MERGED from [com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\23890166d6a8b14dee3091bd6ffdea5c\transformed\jetified-billing-6.0.1\AndroidManifest.xml:10:5-67
MERGED from [com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\23890166d6a8b14dee3091bd6ffdea5c\transformed\jetified-billing-6.0.1\AndroidManifest.xml:10:5-67
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:21:22-64
queries
ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:26:5-28:15
MERGED from [com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bca5bb2c25cf61ac91ecd47130eaf591\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:30:5-34:15
MERGED from [com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bca5bb2c25cf61ac91ecd47130eaf591\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:30:5-34:15
MERGED from [androidx.camera:camera-extensions:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\54b9491a3c7a79413b727cd19f2cf1e8\transformed\jetified-camera-extensions-1.2.2\AndroidManifest.xml:22:5-26:15
MERGED from [androidx.camera:camera-extensions:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\54b9491a3c7a79413b727cd19f2cf1e8\transformed\jetified-camera-extensions-1.2.2\AndroidManifest.xml:22:5-26:15
MERGED from [com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\23890166d6a8b14dee3091bd6ffdea5c\transformed\jetified-billing-6.0.1\AndroidManifest.xml:12:5-16:15
MERGED from [com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\23890166d6a8b14dee3091bd6ffdea5c\transformed\jetified-billing-6.0.1\AndroidManifest.xml:12:5-16:15
package#com.facebook.katana
ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:27:9-55
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:27:18-52
application
ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:30:5-84:19
INJECTED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:30:5-84:19
MERGED from [com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:11:5-30:19
MERGED from [com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:11:5-30:19
MERGED from [com.stripe:stripe-3ds2-android:2.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\7f344a36bb06c1b19b3d8dc344e8ef3c\transformed\jetified-stripe-3ds2-android-2.7.2\AndroidManifest.xml:11:5-18:19
MERGED from [com.stripe:stripe-3ds2-android:2.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\7f344a36bb06c1b19b3d8dc344e8ef3c\transformed\jetified-stripe-3ds2-android-2.7.2\AndroidManifest.xml:11:5-18:19
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\dfce37622a563a24f0c8e9b3351fbc6a\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:26:5-30:19
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\dfce37622a563a24f0c8e9b3351fbc6a\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:26:5-30:19
MERGED from [com.google.android.material:material:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\1c5b7e64d37c02b896c3a005a7dfbcf9\transformed\material-1.7.0\AndroidManifest.xml:24:5-20
MERGED from [com.google.android.material:material:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\1c5b7e64d37c02b896c3a005a7dfbcf9\transformed\material-1.7.0\AndroidManifest.xml:24:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\2428af63cc0a3c36e460feea781f693a\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\2428af63cc0a3c36e460feea781f693a\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [com.google.android.libraries.places:places:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\245b102fe60f0031ab708afcac9ce0b9\transformed\jetified-places-3.0.0\AndroidManifest.xml:14:5-22:19
MERGED from [com.google.android.libraries.places:places:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\245b102fe60f0031ab708afcac9ce0b9\transformed\jetified-places-3.0.0\AndroidManifest.xml:14:5-22:19
MERGED from [io.insert-koin:koin-android:3.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2fbb1a4b8ac61e515c75ed416a4f43b0\transformed\jetified-koin-android-3.3.0\AndroidManifest.xml:7:5-20
MERGED from [io.insert-koin:koin-android:3.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2fbb1a4b8ac61e515c75ed416a4f43b0\transformed\jetified-koin-android-3.3.0\AndroidManifest.xml:7:5-20
MERGED from [com.github.simonebortolin:FlowLayoutManager:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\4948fc7a26504540194a499515390cba\transformed\jetified-FlowLayoutManager-1.8.0\AndroidManifest.xml:9:5-10:19
MERGED from [com.github.simonebortolin:FlowLayoutManager:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\4948fc7a26504540194a499515390cba\transformed\jetified-FlowLayoutManager-1.8.0\AndroidManifest.xml:9:5-10:19
MERGED from [com.airbnb.android:lottie:6.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\66a3728dbd899e31bbf8f1355aee8030\transformed\jetified-lottie-6.0.0\AndroidManifest.xml:9:5-20
MERGED from [com.airbnb.android:lottie:6.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\66a3728dbd899e31bbf8f1355aee8030\transformed\jetified-lottie-6.0.0\AndroidManifest.xml:9:5-20
MERGED from [com.tbuonomo:dotsindicator:4.3] C:\Users\<USER>\.gradle\caches\transforms-3\a910403c30b7fd7ee8d62fed1c87cccb\transformed\jetified-dotsindicator-4.3-debug\AndroidManifest.xml:9:5-47
MERGED from [com.tbuonomo:dotsindicator:4.3] C:\Users\<USER>\.gradle\caches\transforms-3\a910403c30b7fd7ee8d62fed1c87cccb\transformed\jetified-dotsindicator-4.3-debug\AndroidManifest.xml:9:5-47
MERGED from [com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f6fa1e240580d875b9faf8d016b434a\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f6fa1e240580d875b9faf8d016b434a\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bca5bb2c25cf61ac91ecd47130eaf591\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:36:5-42:19
MERGED from [com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bca5bb2c25cf61ac91ecd47130eaf591\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:36:5-42:19
MERGED from [com.google.android.play:app-update-ktx:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\5cf56c4e9c2749bf1f05cde0b3175e9e\transformed\jetified-app-update-ktx-2.0.1\AndroidManifest.xml:11:5-12:19
MERGED from [com.google.android.play:app-update-ktx:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\5cf56c4e9c2749bf1f05cde0b3175e9e\transformed\jetified-app-update-ktx-2.0.1\AndroidManifest.xml:11:5-12:19
MERGED from [com.github.bumptech.glide:glide:4.14.2] C:\Users\<USER>\.gradle\caches\transforms-3\59f9204e4302ab64a1b44acd0e33ff38\transformed\jetified-glide-4.14.2\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:glide:4.14.2] C:\Users\<USER>\.gradle\caches\transforms-3\59f9204e4302ab64a1b44acd0e33ff38\transformed\jetified-glide-4.14.2\AndroidManifest.xml:9:5-20
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\aeed75bc1d8b17705d6e8b075b1be815\transformed\jetified-play-services-location-21.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\aeed75bc1d8b17705d6e8b075b1be815\transformed\jetified-play-services-location-21.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-fitness:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4018ae0a1966300199c2b3e05a6d81e9\transformed\jetified-play-services-fitness-20.0.0\AndroidManifest.xml:22:5-26:19
MERGED from [com.google.android.gms:play-services-fitness:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4018ae0a1966300199c2b3e05a6d81e9\transformed\jetified-play-services-fitness-20.0.0\AndroidManifest.xml:22:5-26:19
MERGED from [com.google.android.gms:play-services-vision:20.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\89198b049f9332bca64336be6116f748\transformed\jetified-play-services-vision-20.1.3\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-vision:20.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\89198b049f9332bca64336be6116f748\transformed\jetified-play-services-vision-20.1.3\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-mlkit-text-recognition-common:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b4bd5f6b063655bfefc8564cd709cc40\transformed\jetified-play-services-mlkit-text-recognition-common-18.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.android.gms:play-services-mlkit-text-recognition-common:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b4bd5f6b063655bfefc8564cd709cc40\transformed\jetified-play-services-mlkit-text-recognition-common-18.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:vision-common:17.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c3691d46c450c8405e6a322b681b222a\transformed\jetified-vision-common-17.2.1\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:vision-common:17.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c3691d46c450c8405e6a322b681b222a\transformed\jetified-vision-common-17.2.1\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c505a1ac40223f3f4347c0c22e87fce\transformed\jetified-common-18.5.0\AndroidManifest.xml:8:5-24:19
MERGED from [com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c505a1ac40223f3f4347c0c22e87fce\transformed\jetified-common-18.5.0\AndroidManifest.xml:8:5-24:19
MERGED from [com.google.firebase:firebase-auth-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\15d4f429e740d2d9a84daaaefe71c783\transformed\jetified-firebase-auth-ktx-21.1.0\AndroidManifest.xml:7:5-15:19
MERGED from [com.google.firebase:firebase-auth-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\15d4f429e740d2d9a84daaaefe71c783\transformed\jetified-firebase-auth-ktx-21.1.0\AndroidManifest.xml:7:5-15:19
MERGED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:26:5-71:19
MERGED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:26:5-71:19
MERGED from [com.google.android.gms:play-services-fido:19.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\b1eb8bd6c0710cce723f554ae768195b\transformed\jetified-play-services-fido-19.0.1\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:19.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\b1eb8bd6c0710cce723f554ae768195b\transformed\jetified-play-services-fido-19.0.1\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-vision-common:19.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\781069a29b67609796d15ed893750924\transformed\jetified-play-services-vision-common-19.1.3\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-vision-common:19.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\781069a29b67609796d15ed893750924\transformed\jetified-play-services-vision-common-19.1.3\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-messaging-ktx:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\c80935087eb2baf2ce8d5fda4c1695fb\transformed\jetified-firebase-messaging-ktx-23.1.1\AndroidManifest.xml:24:5-32:19
MERGED from [com.google.firebase:firebase-messaging-ktx:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\c80935087eb2baf2ce8d5fda4c1695fb\transformed\jetified-firebase-messaging-ktx-23.1.1\AndroidManifest.xml:24:5-32:19
MERGED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:30:5-59:19
MERGED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:30:5-59:19
MERGED from [com.google.android.gms:play-services-clearcut:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\00c62090a3372c4b2e84fc74d20c66c4\transformed\jetified-play-services-clearcut-17.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-clearcut:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\00c62090a3372c4b2e84fc74d20c66c4\transformed\jetified-play-services-clearcut-17.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-phenotype:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5606aff7220fb34ade507a261b563053\transformed\jetified-play-services-phenotype-17.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-phenotype:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5606aff7220fb34ade507a261b563053\transformed\jetified-play-services-phenotype-17.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-safetynet:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0e397d89534d8dd921d128a204dc17e5\transformed\jetified-play-services-safetynet-17.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-safetynet:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0e397d89534d8dd921d128a204dc17e5\transformed\jetified-play-services-safetynet-17.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5296fb8d536108245faa30c6d8197de1\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5296fb8d536108245faa30c6d8197de1\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.play:app-update:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\9c5637f4640a23ae750c2105a3feb6df\transformed\jetified-app-update-2.0.1\AndroidManifest.xml:11:5-12:19
MERGED from [com.google.android.play:app-update:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\9c5637f4640a23ae750c2105a3feb6df\transformed\jetified-app-update-2.0.1\AndroidManifest.xml:11:5-12:19
MERGED from [com.google.firebase:firebase-crashlytics-ktx:18.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\7a08c1f471d4aed10f5a62d4bdfd7cc0\transformed\jetified-firebase-crashlytics-ktx-18.3.5\AndroidManifest.xml:22:5-30:19
MERGED from [com.google.firebase:firebase-crashlytics-ktx:18.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\7a08c1f471d4aed10f5a62d4bdfd7cc0\transformed\jetified-firebase-crashlytics-ktx-18.3.5\AndroidManifest.xml:22:5-30:19
MERGED from [com.google.firebase:firebase-analytics-ktx:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d0398be7ec17550451ee5b14d880cfc\transformed\jetified-firebase-analytics-ktx-21.2.0\AndroidManifest.xml:7:5-15:19
MERGED from [com.google.firebase:firebase-analytics-ktx:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d0398be7ec17550451ee5b14d880cfc\transformed\jetified-firebase-analytics-ktx-21.2.0\AndroidManifest.xml:7:5-15:19
MERGED from [com.google.firebase:firebase-common-ktx:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b2a903c9b0acab1e7698691d1a2c986\transformed\jetified-firebase-common-ktx-20.3.0\AndroidManifest.xml:10:5-18:19
MERGED from [com.google.firebase:firebase-common-ktx:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b2a903c9b0acab1e7698691d1a2c986\transformed\jetified-firebase-common-ktx-20.3.0\AndroidManifest.xml:10:5-18:19
MERGED from [com.google.firebase:firebase-crashlytics:18.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\967d8355743d170a894a29b4b1a80382\transformed\jetified-firebase-crashlytics-18.3.5\AndroidManifest.xml:13:5-21:19
MERGED from [com.google.firebase:firebase-crashlytics:18.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\967d8355743d170a894a29b4b1a80382\transformed\jetified-firebase-crashlytics-18.3.5\AndroidManifest.xml:13:5-21:19
MERGED from [com.google.firebase:firebase-analytics:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7ad3c7c712cc6d61b042196a1cabbfd5\transformed\jetified-firebase-analytics-21.2.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-analytics:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7ad3c7c712cc6d61b042196a1cabbfd5\transformed\jetified-firebase-analytics-21.2.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\168037942f327435dcf9fbba3d0dde22\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\168037942f327435dcf9fbba3d0dde22\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.firebase:firebase-installations:17.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\f175c9d0e7eca696c235389c9c14325d\transformed\jetified-firebase-installations-17.1.2\AndroidManifest.xml:13:5-21:19
MERGED from [com.google.firebase:firebase-installations:17.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\f175c9d0e7eca696c235389c9c14325d\transformed\jetified-firebase-installations-17.1.2\AndroidManifest.xml:13:5-21:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\36640c11b54047d7650312d271b9d737\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\36640c11b54047d7650312d271b9d737\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\b6cf1c5e33857039c8761daab9336c00\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:23:5-31:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\b6cf1c5e33857039c8761daab9336c00\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:23:5-31:19
MERGED from [com.google.firebase:firebase-common:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\422e0883ce3188039e67d46c46566547\transformed\jetified-firebase-common-20.3.0\AndroidManifest.xml:24:5-37:19
MERGED from [com.google.firebase:firebase-common:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\422e0883ce3188039e67d46c46566547\transformed\jetified-firebase-common-20.3.0\AndroidManifest.xml:24:5-37:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d1c4513013d397eaa0fbbd356cbef4a\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d1c4513013d397eaa0fbbd356cbef4a\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [androidx.camera:camera-extensions:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\54b9491a3c7a79413b727cd19f2cf1e8\transformed\jetified-camera-extensions-1.2.2\AndroidManifest.xml:28:5-32:19
MERGED from [androidx.camera:camera-extensions:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\54b9491a3c7a79413b727cd19f2cf1e8\transformed\jetified-camera-extensions-1.2.2\AndroidManifest.xml:28:5-32:19
MERGED from [androidx.camera:camera-camera2:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\ee28ca93c07bf81d90ff8f6019728918\transformed\jetified-camera-camera2-1.2.2\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-camera2:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\ee28ca93c07bf81d90ff8f6019728918\transformed\jetified-camera-camera2-1.2.2\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2f30601676b1559d6170091243eee16\transformed\jetified-camera-core-1.2.2\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2f30601676b1559d6170091243eee16\transformed\jetified-camera-core-1.2.2\AndroidManifest.xml:23:5-34:19
MERGED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\80572253c32e71649c1c4bf8e27add88\transformed\jetified-play-services-measurement-sdk-21.2.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\80572253c32e71649c1c4bf8e27add88\transformed\jetified-play-services-measurement-sdk-21.2.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-impl:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ebc0a470e1cbd9a5ad219bc5fd5997fd\transformed\jetified-play-services-measurement-impl-21.2.0\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-measurement-impl:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ebc0a470e1cbd9a5ad219bc5fd5997fd\transformed\jetified-play-services-measurement-impl-21.2.0\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\daa3e843a443636df76a26d3b0bcf1e7\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\daa3e843a443636df76a26d3b0bcf1e7\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\549ede32db9c3acb303de4fa6d2a7dfa\transformed\jetified-ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\549ede32db9c3acb303de4fa6d2a7dfa\transformed\jetified-ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\7ada1d27f1fad82fa455b667be34e866\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\7ada1d27f1fad82fa455b667be34e866\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\078811cffc78e28bc7870a96ed097257\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\078811cffc78e28bc7870a96ed097257\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\415c161ff15d16996587df02a2a7fdc1\transformed\jetified-window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\415c161ff15d16996587df02a2a7fdc1\transformed\jetified-window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4b7543783d7e597efaf2f46cf5f3847\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4b7543783d7e597efaf2f46cf5f3847\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\5894d67a40de79c92f240f18414ffdf3\transformed\jetified-play-services-tasks-18.0.2\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\5894d67a40de79c92f240f18414ffdf3\transformed\jetified-play-services-tasks-18.0.2\AndroidManifest.xml:5:5-20
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d41c20a44acb8d35f05eb6f2f49b06ae\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d41c20a44acb8d35f05eb6f2f49b06ae\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4471cce94b29aeb47706814ba2b6c808\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4471cce94b29aeb47706814ba2b6c808\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\44ab81bc213b7d1a040dcf2f8680989d\transformed\jetified-play-services-measurement-base-21.2.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\44ab81bc213b7d1a040dcf2f8680989d\transformed\jetified-play-services-measurement-base-21.2.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\3eddf5b8a9d33eb11d1830cb5e4fd1aa\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\3eddf5b8a9d33eb11d1830cb5e4fd1aa\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\0d2b250ca7a066e4f86c5139c779b394\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\0d2b250ca7a066e4f86c5139c779b394\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.github.bumptech.glide:gifdecoder:4.14.2] C:\Users\<USER>\.gradle\caches\transforms-3\d97c3db64fd63c39af8a8f807d4f9588\transformed\jetified-gifdecoder-4.14.2\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:gifdecoder:4.14.2] C:\Users\<USER>\.gradle\caches\transforms-3\d97c3db64fd63c39af8a8f807d4f9588\transformed\jetified-gifdecoder-4.14.2\AndroidManifest.xml:9:5-20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\c900719aaf192cd5689ec37a91c858c8\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\c900719aaf192cd5689ec37a91c858c8\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\23890166d6a8b14dee3091bd6ffdea5c\transformed\jetified-billing-6.0.1\AndroidManifest.xml:18:5-28:19
MERGED from [com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\23890166d6a8b14dee3091bd6ffdea5c\transformed\jetified-billing-6.0.1\AndroidManifest.xml:18:5-28:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e0f5c7c79df0748d229f2beda36ab74d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e0f5c7c79df0748d229f2beda36ab74d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e3b38192b92eeb3fc9e8187da50ce6a9\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e3b38192b92eeb3fc9e8187da50ce6a9\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.maps.android:android-maps-utils:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d812f540c2906d43adfa5531b198632c\transformed\jetified-android-maps-utils-2.2.0\AndroidManifest.xml:11:5-15:19
MERGED from [com.google.maps.android:android-maps-utils:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d812f540c2906d43adfa5531b198632c\transformed\jetified-android-maps-utils-2.2.0\AndroidManifest.xml:11:5-15:19
MERGED from [com.google.android.play:core-common:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\dc72e552015300076bb41fda69eedbd2\transformed\jetified-core-common-2.0.2\AndroidManifest.xml:11:5-19:19
MERGED from [com.google.android.play:core-common:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\dc72e552015300076bb41fda69eedbd2\transformed\jetified-core-common-2.0.2\AndroidManifest.xml:11:5-19:19
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\transforms-3\d232e3111dc78fa8c6f2ad38a2151a17\transformed\jetified-image-1.0.0-beta1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\transforms-3\d232e3111dc78fa8c6f2ad38a2151a17\transformed\jetified-image-1.0.0-beta1\AndroidManifest.xml:7:5-20
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml
	android:requestLegacyExternalStorage
		ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:39:9-52
	android:roundIcon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:37:9-54
	android:icon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:35:9-43
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4b7543783d7e597efaf2f46cf5f3847\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:38:9-35
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:36:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:34:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:42:9-28
	android:allowBackup
		ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:32:9-36
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:40:9-40
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:33:9-65
	android:usesCleartextTraffic
		ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:41:9-44
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:31:9-46
activity#com.manaknight.app.MainActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:43:9-60:20
	android:screenOrientation
		ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:46:13-49
	android:launchMode
		ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:50:13-44
	android:hardwareAccelerated
		ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:49:13-47
	android:windowSoftInputMode
		ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:48:13-55
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:45:13-36
	tools:ignore
		ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:47:13-53
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:44:13-59
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:51:13-55:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:52:17-69
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:52:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:54:17-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:54:27-74
meta-data#android.app.lib_name
ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:57:13-59:36
	android:value
		ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:59:17-33
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:58:17-52
meta-data#com.google.firebase.messaging.default_notification_icon
ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:62:9-64:58
	android:resource
		ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:64:13-55
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:63:13-83
service#com.manaknight.app.fcm.MyFirebasePushNotifications
ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:76:9-83:19
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:78:13-37
	tools:ignore
		ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:79:13-42
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:77:13-78
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:80:13-82:29
action#com.google.firebase.MESSAGING_EVENT
ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:81:17-78
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:81:25-75
uses-sdk
INJECTED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml
MERGED from [com.github.sakshampruthi:custom-loading-screen:1.16] C:\Users\<USER>\.gradle\caches\transforms-3\d596ae0a4869b18a70be323e8a610021\transformed\jetified-custom-loading-screen-1.16\AndroidManifest.xml:7:5-9:41
MERGED from [com.github.sakshampruthi:custom-loading-screen:1.16] C:\Users\<USER>\.gradle\caches\transforms-3\d596ae0a4869b18a70be323e8a610021\transformed\jetified-custom-loading-screen-1.16\AndroidManifest.xml:7:5-9:41
MERGED from [com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.stripe:stripe-3ds2-android:2.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\7f344a36bb06c1b19b3d8dc344e8ef3c\transformed\jetified-stripe-3ds2-android-2.7.2\AndroidManifest.xml:7:5-9:41
MERGED from [com.stripe:stripe-3ds2-android:2.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\7f344a36bb06c1b19b3d8dc344e8ef3c\transformed\jetified-stripe-3ds2-android-2.7.2\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.databinding:viewbinding:8.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d94a1d1dd722c506b8ed1d20403b573e\transformed\jetified-viewbinding-8.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d94a1d1dd722c506b8ed1d20403b573e\transformed\jetified-viewbinding-8.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\dfce37622a563a24f0c8e9b3351fbc6a\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:22:5-24:41
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\dfce37622a563a24f0c8e9b3351fbc6a\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:22:5-24:41
MERGED from [androidx.navigation:navigation-common:2.7.4] C:\Users\<USER>\.gradle\caches\transforms-3\2e728102c794137111e02ef3cc7ce58a\transformed\navigation-common-2.7.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.4] C:\Users\<USER>\.gradle\caches\transforms-3\2e728102c794137111e02ef3cc7ce58a\transformed\navigation-common-2.7.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.4] C:\Users\<USER>\.gradle\caches\transforms-3\d07f2507d796555fa3e9b9293e3cd644\transformed\navigation-fragment-2.7.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.4] C:\Users\<USER>\.gradle\caches\transforms-3\d07f2507d796555fa3e9b9293e3cd644\transformed\navigation-fragment-2.7.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.4] C:\Users\<USER>\.gradle\caches\transforms-3\cafa83fd917f253b5ba51f41700f6128\transformed\navigation-runtime-2.7.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.4] C:\Users\<USER>\.gradle\caches\transforms-3\cafa83fd917f253b5ba51f41700f6128\transformed\navigation-runtime-2.7.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.4] C:\Users\<USER>\.gradle\caches\transforms-3\e27eccadf47d15b674d702d3856da599\transformed\navigation-common-ktx-2.7.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.4] C:\Users\<USER>\.gradle\caches\transforms-3\e27eccadf47d15b674d702d3856da599\transformed\navigation-common-ktx-2.7.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.4] C:\Users\<USER>\.gradle\caches\transforms-3\3754230fa958c0a5539f1708b5c1c207\transformed\navigation-fragment-ktx-2.7.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.4] C:\Users\<USER>\.gradle\caches\transforms-3\3754230fa958c0a5539f1708b5c1c207\transformed\navigation-fragment-ktx-2.7.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.4] C:\Users\<USER>\.gradle\caches\transforms-3\df7a7742007c25ef02b00676b95a8b64\transformed\navigation-runtime-ktx-2.7.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.4] C:\Users\<USER>\.gradle\caches\transforms-3\df7a7742007c25ef02b00676b95a8b64\transformed\navigation-runtime-ktx-2.7.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.4] C:\Users\<USER>\.gradle\caches\transforms-3\01270e06c1369120c2a156bc8f0baf75\transformed\jetified-navigation-compose-2.7.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.4] C:\Users\<USER>\.gradle\caches\transforms-3\01270e06c1369120c2a156bc8f0baf75\transformed\jetified-navigation-compose-2.7.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.4] C:\Users\<USER>\.gradle\caches\transforms-3\aecc66a34563bb4aa24d31833e6be989\transformed\navigation-ui-ktx-2.7.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.4] C:\Users\<USER>\.gradle\caches\transforms-3\aecc66a34563bb4aa24d31833e6be989\transformed\navigation-ui-ktx-2.7.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.4] C:\Users\<USER>\.gradle\caches\transforms-3\2d0b9e5b251e403c37690fd551fa5112\transformed\navigation-ui-2.7.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.4] C:\Users\<USER>\.gradle\caches\transforms-3\2d0b9e5b251e403c37690fd551fa5112\transformed\navigation-ui-2.7.4\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\1c5b7e64d37c02b896c3a005a7dfbcf9\transformed\material-1.7.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.material:material:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\1c5b7e64d37c02b896c3a005a7dfbcf9\transformed\material-1.7.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\2428af63cc0a3c36e460feea781f693a\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\2428af63cc0a3c36e460feea781f693a\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.libraries.places:places:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\245b102fe60f0031ab708afcac9ce0b9\transformed\jetified-places-3.0.0\AndroidManifest.xml:7:5-44
MERGED from [com.google.android.libraries.places:places:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\245b102fe60f0031ab708afcac9ce0b9\transformed\jetified-places-3.0.0\AndroidManifest.xml:7:5-44
MERGED from [io.insert-koin:koin-android:3.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2fbb1a4b8ac61e515c75ed416a4f43b0\transformed\jetified-koin-android-3.3.0\AndroidManifest.xml:5:5-44
MERGED from [io.insert-koin:koin-android:3.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2fbb1a4b8ac61e515c75ed416a4f43b0\transformed\jetified-koin-android-3.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-view:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\2b745688d27acb49059c05228eef7303\transformed\jetified-camera-view-1.2.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-view:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\2b745688d27acb49059c05228eef7303\transformed\jetified-camera-view-1.2.2\AndroidManifest.xml:20:5-44
MERGED from [com.github.simonebortolin:FlowLayoutManager:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\4948fc7a26504540194a499515390cba\transformed\jetified-FlowLayoutManager-1.8.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.simonebortolin:FlowLayoutManager:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\4948fc7a26504540194a499515390cba\transformed\jetified-FlowLayoutManager-1.8.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.airbnb.android:lottie:6.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\66a3728dbd899e31bbf8f1355aee8030\transformed\jetified-lottie-6.0.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.airbnb.android:lottie:6.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\66a3728dbd899e31bbf8f1355aee8030\transformed\jetified-lottie-6.0.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.tbuonomo:dotsindicator:4.3] C:\Users\<USER>\.gradle\caches\transforms-3\a910403c30b7fd7ee8d62fed1c87cccb\transformed\jetified-dotsindicator-4.3-debug\AndroidManifest.xml:5:5-7:41
MERGED from [com.tbuonomo:dotsindicator:4.3] C:\Users\<USER>\.gradle\caches\transforms-3\a910403c30b7fd7ee8d62fed1c87cccb\transformed\jetified-dotsindicator-4.3-debug\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\09ee94349df8be93cc14578888d37864\transformed\jetified-appcompat-resources-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\09ee94349df8be93cc14578888d37864\transformed\jetified-appcompat-resources-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\8ba2f5412e2a4ad787869c635535bb6d\transformed\appcompat-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\8ba2f5412e2a4ad787869c635535bb6d\transformed\appcompat-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f6fa1e240580d875b9faf8d016b434a\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f6fa1e240580d875b9faf8d016b434a\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bca5bb2c25cf61ac91ecd47130eaf591\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bca5bb2c25cf61ac91ecd47130eaf591\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\52dd77e3ac8ff42cd18cec69ec02066d\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\52dd77e3ac8ff42cd18cec69ec02066d\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.play:app-update-ktx:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\5cf56c4e9c2749bf1f05cde0b3175e9e\transformed\jetified-app-update-ktx-2.0.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.play:app-update-ktx:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\5cf56c4e9c2749bf1f05cde0b3175e9e\transformed\jetified-app-update-ktx-2.0.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.github.bumptech.glide:glide:4.14.2] C:\Users\<USER>\.gradle\caches\transforms-3\59f9204e4302ab64a1b44acd0e33ff38\transformed\jetified-glide-4.14.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.14.2] C:\Users\<USER>\.gradle\caches\transforms-3\59f9204e4302ab64a1b44acd0e33ff38\transformed\jetified-glide-4.14.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.gms:play-services-mlkit-text-recognition:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\064ab3ca430c0695fe6ffa9cfb590653\transformed\jetified-play-services-mlkit-text-recognition-18.0.2\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-mlkit-text-recognition:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\064ab3ca430c0695fe6ffa9cfb590653\transformed\jetified-play-services-mlkit-text-recognition-18.0.2\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\aeed75bc1d8b17705d6e8b075b1be815\transformed\jetified-play-services-location-21.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\aeed75bc1d8b17705d6e8b075b1be815\transformed\jetified-play-services-location-21.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fitness:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4018ae0a1966300199c2b3e05a6d81e9\transformed\jetified-play-services-fitness-20.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-fitness:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4018ae0a1966300199c2b3e05a6d81e9\transformed\jetified-play-services-fitness-20.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-vision:20.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\89198b049f9332bca64336be6116f748\transformed\jetified-play-services-vision-20.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-vision:20.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\89198b049f9332bca64336be6116f748\transformed\jetified-play-services-vision-20.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-mlkit-text-recognition-common:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b4bd5f6b063655bfefc8564cd709cc40\transformed\jetified-play-services-mlkit-text-recognition-common-18.0.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-mlkit-text-recognition-common:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b4bd5f6b063655bfefc8564cd709cc40\transformed\jetified-play-services-mlkit-text-recognition-common-18.0.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:vision-common:17.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c3691d46c450c8405e6a322b681b222a\transformed\jetified-vision-common-17.2.1\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:vision-common:17.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c3691d46c450c8405e6a322b681b222a\transformed\jetified-vision-common-17.2.1\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c505a1ac40223f3f4347c0c22e87fce\transformed\jetified-common-18.5.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c505a1ac40223f3f4347c0c22e87fce\transformed\jetified-common-18.5.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-auth-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\15d4f429e740d2d9a84daaaefe71c783\transformed\jetified-firebase-auth-ktx-21.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\15d4f429e740d2d9a84daaaefe71c783\transformed\jetified-firebase-auth-ktx-21.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\442c19f05b5ee5d7a42222c5faaaa5f4\transformed\jetified-play-services-auth-api-phone-18.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\442c19f05b5ee5d7a42222c5faaaa5f4\transformed\jetified-play-services-auth-api-phone-18.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\217712a4df42ce61eeefb0b0a4ae3b80\transformed\jetified-play-services-auth-base-18.0.4\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\217712a4df42ce61eeefb0b0a4ae3b80\transformed\jetified-play-services-auth-base-18.0.4\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:19.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\b1eb8bd6c0710cce723f554ae768195b\transformed\jetified-play-services-fido-19.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:19.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\b1eb8bd6c0710cce723f554ae768195b\transformed\jetified-play-services-fido-19.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-vision-common:19.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\781069a29b67609796d15ed893750924\transformed\jetified-play-services-vision-common-19.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-vision-common:19.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\781069a29b67609796d15ed893750924\transformed\jetified-play-services-vision-common-19.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-messaging-ktx:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\c80935087eb2baf2ce8d5fda4c1695fb\transformed\jetified-firebase-messaging-ktx-23.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-messaging-ktx:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\c80935087eb2baf2ce8d5fda4c1695fb\transformed\jetified-firebase-messaging-ktx-23.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.gms:play-services-clearcut:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\00c62090a3372c4b2e84fc74d20c66c4\transformed\jetified-play-services-clearcut-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-clearcut:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\00c62090a3372c4b2e84fc74d20c66c4\transformed\jetified-play-services-clearcut-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-flags:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5e5492193f167549559412ad6082cce4\transformed\jetified-play-services-flags-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-flags:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5e5492193f167549559412ad6082cce4\transformed\jetified-play-services-flags-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-phenotype:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5606aff7220fb34ade507a261b563053\transformed\jetified-play-services-phenotype-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-phenotype:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5606aff7220fb34ade507a261b563053\transformed\jetified-play-services-phenotype-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-safetynet:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0e397d89534d8dd921d128a204dc17e5\transformed\jetified-play-services-safetynet-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-safetynet:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0e397d89534d8dd921d128a204dc17e5\transformed\jetified-play-services-safetynet-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5296fb8d536108245faa30c6d8197de1\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5296fb8d536108245faa30c6d8197de1\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.play:app-update:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\9c5637f4640a23ae750c2105a3feb6df\transformed\jetified-app-update-2.0.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.play:app-update:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\9c5637f4640a23ae750c2105a3feb6df\transformed\jetified-app-update-2.0.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.firebase:firebase-crashlytics-ktx:18.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\7a08c1f471d4aed10f5a62d4bdfd7cc0\transformed\jetified-firebase-crashlytics-ktx-18.3.5\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-crashlytics-ktx:18.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\7a08c1f471d4aed10f5a62d4bdfd7cc0\transformed\jetified-firebase-crashlytics-ktx-18.3.5\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-analytics-ktx:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d0398be7ec17550451ee5b14d880cfc\transformed\jetified-firebase-analytics-ktx-21.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics-ktx:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d0398be7ec17550451ee5b14d880cfc\transformed\jetified-firebase-analytics-ktx-21.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b2a903c9b0acab1e7698691d1a2c986\transformed\jetified-firebase-common-ktx-20.3.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-common-ktx:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b2a903c9b0acab1e7698691d1a2c986\transformed\jetified-firebase-common-ktx-20.3.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-crashlytics:18.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\967d8355743d170a894a29b4b1a80382\transformed\jetified-firebase-crashlytics-18.3.5\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-crashlytics:18.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\967d8355743d170a894a29b4b1a80382\transformed\jetified-firebase-crashlytics-18.3.5\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-analytics:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7ad3c7c712cc6d61b042196a1cabbfd5\transformed\jetified-firebase-analytics-21.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7ad3c7c712cc6d61b042196a1cabbfd5\transformed\jetified-firebase-analytics-21.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\168037942f327435dcf9fbba3d0dde22\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\168037942f327435dcf9fbba3d0dde22\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-installations:17.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\f175c9d0e7eca696c235389c9c14325d\transformed\jetified-firebase-installations-17.1.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-installations:17.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\f175c9d0e7eca696c235389c9c14325d\transformed\jetified-firebase-installations-17.1.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\36640c11b54047d7650312d271b9d737\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\36640c11b54047d7650312d271b9d737\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\b6cf1c5e33857039c8761daab9336c00\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\b6cf1c5e33857039c8761daab9336c00\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-common:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\422e0883ce3188039e67d46c46566547\transformed\jetified-firebase-common-20.3.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-common:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\422e0883ce3188039e67d46c46566547\transformed\jetified-firebase-common-20.3.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.mlkit:vision-interfaces:16.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5fe3db25fce9db029f09aa0094f8691c\transformed\jetified-vision-interfaces-16.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:vision-interfaces:16.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5fe3db25fce9db029f09aa0094f8691c\transformed\jetified-vision-interfaces-16.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\195a69fff94f2bfb0d0e8bddaecb08d4\transformed\jetified-firebase-installations-interop-17.1.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-installations-interop:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\195a69fff94f2bfb0d0e8bddaecb08d4\transformed\jetified-firebase-installations-interop-17.1.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\4d427ed86a6d184dccce4dcab3eba6c4\transformed\jetified-play-services-cloud-messaging-17.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\4d427ed86a6d184dccce4dcab3eba6c4\transformed\jetified-play-services-cloud-messaging-17.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d1c4513013d397eaa0fbbd356cbef4a\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d1c4513013d397eaa0fbbd356cbef4a\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-systemuicontroller:0.33.0-alpha] C:\Users\<USER>\.gradle\caches\transforms-3\b5d0ce2e99c1e6f542db90966d336f81\transformed\jetified-accompanist-systemuicontroller-0.33.0-alpha\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-systemuicontroller:0.33.0-alpha] C:\Users\<USER>\.gradle\caches\transforms-3\b5d0ce2e99c1e6f542db90966d336f81\transformed\jetified-accompanist-systemuicontroller-0.33.0-alpha\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-extensions:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\54b9491a3c7a79413b727cd19f2cf1e8\transformed\jetified-camera-extensions-1.2.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-extensions:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\54b9491a3c7a79413b727cd19f2cf1e8\transformed\jetified-camera-extensions-1.2.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-camera2:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\ee28ca93c07bf81d90ff8f6019728918\transformed\jetified-camera-camera2-1.2.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-camera2:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\ee28ca93c07bf81d90ff8f6019728918\transformed\jetified-camera-camera2-1.2.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-lifecycle:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\11cda86714253a9d3d26b6bb06cc5cac\transformed\jetified-camera-lifecycle-1.2.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-lifecycle:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\11cda86714253a9d3d26b6bb06cc5cac\transformed\jetified-camera-lifecycle-1.2.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-core:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2f30601676b1559d6170091243eee16\transformed\jetified-camera-core-1.2.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2f30601676b1559d6170091243eee16\transformed\jetified-camera-core-1.2.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9707284c2df373486e3d74a080fb4e6d\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9707284c2df373486e3d74a080fb4e6d\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dacd3ba96c55d27662761824276657e5\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dacd3ba96c55d27662761824276657e5\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\80572253c32e71649c1c4bf8e27add88\transformed\jetified-play-services-measurement-sdk-21.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\80572253c32e71649c1c4bf8e27add88\transformed\jetified-play-services-measurement-sdk-21.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ebc0a470e1cbd9a5ad219bc5fd5997fd\transformed\jetified-play-services-measurement-impl-21.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ebc0a470e1cbd9a5ad219bc5fd5997fd\transformed\jetified-play-services-measurement-impl-21.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\daa3e843a443636df76a26d3b0bcf1e7\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\daa3e843a443636df76a26d3b0bcf1e7\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\eb45b79d35ab87f67935e779c9e8bdf3\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\eb45b79d35ab87f67935e779c9e8bdf3\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bbc1907e82227ca035f4be4cfe067de0\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bbc1907e82227ca035f4be4cfe067de0\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\32135bc568b9cde0c2f5e949dfb59663\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\32135bc568b9cde0c2f5e949dfb59663\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-window-size-class-android:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\7a6728e66b15c37af970a20ee5332265\transformed\jetified-material3-window-size-class-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3-window-size-class-android:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\7a6728e66b15c37af970a20ee5332265\transformed\jetified-material3-window-size-class-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3-android:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\b287a1edc0bc12e0df878b4dbbb26e19\transformed\jetified-material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\b287a1edc0bc12e0df878b4dbbb26e19\transformed\jetified-material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\05b1a8e3042ec632090b4eb5f8137b2b\transformed\jetified-material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\05b1a8e3042ec632090b4eb5f8137b2b\transformed\jetified-material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\ac02a7a94422dcce40cdd6c0000e16e2\transformed\jetified-material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\ac02a7a94422dcce40cdd6c0000e16e2\transformed\jetified-material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\81cba43246bfeb52a6860a7c49256468\transformed\jetified-material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\81cba43246bfeb52a6860a7c49256468\transformed\jetified-material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\96e1d1cc1146c2c08c7d053927fc0308\transformed\jetified-runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\96e1d1cc1146c2c08c7d053927fc0308\transformed\jetified-runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\070bf93e2411a88dafbd5c4c7942de93\transformed\jetified-ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\070bf93e2411a88dafbd5c4c7942de93\transformed\jetified-ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\b75197b9c9125fc0157db5bd697ca3e3\transformed\jetified-ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\b75197b9c9125fc0157db5bd697ca3e3\transformed\jetified-ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\3b3d207575e904ff44cfc28f4120b438\transformed\jetified-ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\3b3d207575e904ff44cfc28f4120b438\transformed\jetified-ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\cbde9d70a79d9e24cfe6ac77c925900a\transformed\jetified-ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\cbde9d70a79d9e24cfe6ac77c925900a\transformed\jetified-ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\1bd64310f22a0763bf1c9e96e8b4eb71\transformed\jetified-runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\1bd64310f22a0763bf1c9e96e8b4eb71\transformed\jetified-runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-livedata:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\082736c6186f577f1bd1ab12faf6fe90\transformed\jetified-runtime-livedata-1.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-livedata:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\082736c6186f577f1bd1ab12faf6fe90\transformed\jetified-runtime-livedata-1.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\38f6938632726e453470b901b774bc28\transformed\jetified-ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\38f6938632726e453470b901b774bc28\transformed\jetified-ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\962c984fdf6b039b6ea374cccc04d637\transformed\jetified-ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\962c984fdf6b039b6ea374cccc04d637\transformed\jetified-ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\b13e36d21503c40a51f9490c0fb5ce2b\transformed\jetified-ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\b13e36d21503c40a51f9490c0fb5ce2b\transformed\jetified-ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\70bc9aa5687d756269064d6bb99120e2\transformed\jetified-ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\70bc9aa5687d756269064d6bb99120e2\transformed\jetified-ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\549ede32db9c3acb303de4fa6d2a7dfa\transformed\jetified-ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\549ede32db9c3acb303de4fa6d2a7dfa\transformed\jetified-ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\c6d725e0e08b97655e308af475197105\transformed\jetified-material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\c6d725e0e08b97655e308af475197105\transformed\jetified-material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\a1380ac7d4a61052416a4d1b59c9b0c8\transformed\jetified-animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\a1380ac7d4a61052416a4d1b59c9b0c8\transformed\jetified-animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ce0b2e218c7b2a09736555bcc0b4970\transformed\jetified-animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ce0b2e218c7b2a09736555bcc0b4970\transformed\jetified-animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\f1f79afb44259c4de9e26d81ad9b5d15\transformed\jetified-foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\f1f79afb44259c4de9e26d81ad9b5d15\transformed\jetified-foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\344cacb528c3e1b11175f0c06e0c1359\transformed\jetified-foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\344cacb528c3e1b11175f0c06e0c1359\transformed\jetified-foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\0d25d57bf2baff79cf0e8eba55181641\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\0d25d57bf2baff79cf0e8eba55181641\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\7ada1d27f1fad82fa455b667be34e866\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\7ada1d27f1fad82fa455b667be34e866\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\078811cffc78e28bc7870a96ed097257\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\078811cffc78e28bc7870a96ed097257\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\6126ffc5a4df2a2abdf3423b0d8904c9\transformed\jetified-lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\6126ffc5a4df2a2abdf3423b0d8904c9\transformed\jetified-lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\12a373fb55a564c3fe263b3f6935d1db\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\12a373fb55a564c3fe263b3f6935d1db\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\7eb0be57541310c55aab8a6fe7654e38\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\7eb0be57541310c55aab8a6fe7654e38\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\aaa1b22d2b09aff067ec5daf6e488cfc\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\aaa1b22d2b09aff067ec5daf6e488cfc\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\d8f7ad7e9cfa4852fc12065d57ca4a88\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\d8f7ad7e9cfa4852fc12065d57ca4a88\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\e0254ede74c9b8f5b64860fd23de97f2\transformed\jetified-lifecycle-viewmodel-compose-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\e0254ede74c9b8f5b64860fd23de97f2\transformed\jetified-lifecycle-viewmodel-compose-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\d785d3b79612799668071fcb2ce91caa\transformed\jetified-lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\d785d3b79612799668071fcb2ce91caa\transformed\jetified-lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\612da429bcd6dcfc9d3c4f1af6780bf5\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\612da429bcd6dcfc9d3c4f1af6780bf5\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b9da2439d13dda14b327832efc078fdd\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b9da2439d13dda14b327832efc078fdd\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\af559310e2286e826f29fc113b7f4a2d\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\af559310e2286e826f29fc113b7f4a2d\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f4f6aeeb3956b84a487304df665695ac\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f4f6aeeb3956b84a487304df665695ac\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\ba08e4d148ac61b853fdf970301f70c9\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\ba08e4d148ac61b853fdf970301f70c9\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c45c3759094c2057e53f99bee2adf071\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c45c3759094c2057e53f99bee2adf071\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ae1a62bfefe7342250403252aeb82538\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ae1a62bfefe7342250403252aeb82538\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\343cb2f92f12afa4514143982741cd1c\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\343cb2f92f12afa4514143982741cd1c\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\e3103d9432406b321a1e2e5913be8ed6\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\e3103d9432406b321a1e2e5913be8ed6\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\ee0de7eb3f8ea765c583f4b74e820d66\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\ee0de7eb3f8ea765c583f4b74e820d66\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\558ddb4f0e812e19484bb84d65c0d00f\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\558ddb4f0e812e19484bb84d65c0d00f\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\cfa1c7e923fa1317d07bdcd9bd03777b\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\cfa1c7e923fa1317d07bdcd9bd03777b\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\415c161ff15d16996587df02a2a7fdc1\transformed\jetified-window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\415c161ff15d16996587df02a2a7fdc1\transformed\jetified-window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2765a7455ef68c71a1c9e0db21aec1d5\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2765a7455ef68c71a1c9e0db21aec1d5\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\788efde5a45f4c30ec0022beed46df83\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\788efde5a45f4c30ec0022beed46df83\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\513b951cb5326af6c4cb747421bddf5c\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\513b951cb5326af6c4cb747421bddf5c\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5bfca2f7df9cffd8adb370df524988b5\transformed\jetified-core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5bfca2f7df9cffd8adb370df524988b5\transformed\jetified-core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6aa5aa5c226d738ac0b7be23550f05e5\transformed\jetified-autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6aa5aa5c226d738ac0b7be23550f05e5\transformed\jetified-autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4b7543783d7e597efaf2f46cf5f3847\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4b7543783d7e597efaf2f46cf5f3847\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\47aa295ad4f008a6ea3b1b56357692d1\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\47aa295ad4f008a6ea3b1b56357692d1\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\1094bff9c5f3a27eb5dd6ca68de079ae\transformed\jetified-lifecycle-livedata-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\1094bff9c5f3a27eb5dd6ca68de079ae\transformed\jetified-lifecycle-livedata-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\5923f21a1bf8cac115a6d7f3a4dbdf7b\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\5923f21a1bf8cac115a6d7f3a4dbdf7b\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\bea17e79b9eb17c5975c225574f93648\transformed\jetified-lifecycle-runtime-compose-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\bea17e79b9eb17c5975c225574f93648\transformed\jetified-lifecycle-runtime-compose-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\c21ba8f66cefcda1f910a4d64ece4228\transformed\jetified-lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\c21ba8f66cefcda1f910a4d64ece4228\transformed\jetified-lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [com.android.billingclient:billing-ktx:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\f944db2fccf568f9832b5ab32b83578e\transformed\jetified-billing-ktx-6.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.android.billingclient:billing-ktx:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\f944db2fccf568f9832b5ab32b83578e\transformed\jetified-billing-ktx-6.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\5894d67a40de79c92f240f18414ffdf3\transformed\jetified-play-services-tasks-18.0.2\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\5894d67a40de79c92f240f18414ffdf3\transformed\jetified-play-services-tasks-18.0.2\AndroidManifest.xml:4:5-43
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d41c20a44acb8d35f05eb6f2f49b06ae\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d41c20a44acb8d35f05eb6f2f49b06ae\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4471cce94b29aeb47706814ba2b6c808\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4471cce94b29aeb47706814ba2b6c808\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\195d9f9b0cb87448c1e03b6bfc41a24b\transformed\jetified-play-services-measurement-sdk-api-21.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\195d9f9b0cb87448c1e03b6bfc41a24b\transformed\jetified-play-services-measurement-sdk-api-21.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\44ab81bc213b7d1a040dcf2f8680989d\transformed\jetified-play-services-measurement-base-21.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\44ab81bc213b7d1a040dcf2f8680989d\transformed\jetified-play-services-measurement-base-21.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\3eddf5b8a9d33eb11d1830cb5e4fd1aa\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\3eddf5b8a9d33eb11d1830cb5e4fd1aa\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\da507c8fa600d92c1c1664b80a52110b\transformed\fragment-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\da507c8fa600d92c1c1664b80a52110b\transformed\fragment-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\6d349a96672a9169633f047b27fe21ae\transformed\jetified-fragment-ktx-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\6d349a96672a9169633f047b27fe21ae\transformed\jetified-fragment-ktx-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d541a9cc09c6ffbb54db4c38bfa679b\transformed\jetified-activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d541a9cc09c6ffbb54db4c38bfa679b\transformed\jetified-activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\273932cfa347fd030d8f74b6c30f902e\transformed\jetified-activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\273932cfa347fd030d8f74b6c30f902e\transformed\jetified-activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-compose:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\30556913ce86c30d62df9c84fa8696d5\transformed\jetified-activity-compose-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\30556913ce86c30d62df9c84fa8696d5\transformed\jetified-activity-compose-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-splashscreen:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0b38a6f884d3b2956d400ad9386058b6\transformed\jetified-core-splashscreen-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-splashscreen:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0b38a6f884d3b2956d400ad9386058b6\transformed\jetified-core-splashscreen-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\8ce63201cb3fc8f8a03db0835f910fc5\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\8ce63201cb3fc8f8a03db0835f910fc5\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\0d2b250ca7a066e4f86c5139c779b394\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\0d2b250ca7a066e4f86c5139c779b394\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.PhilJay:MPAndroidChart:v3.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7303e81fbffacbcbcb90a66cd126534f\transformed\jetified-MPAndroidChart-v3.1.0\AndroidManifest.xml:16:5-18:41
MERGED from [com.github.PhilJay:MPAndroidChart:v3.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7303e81fbffacbcbcb90a66cd126534f\transformed\jetified-MPAndroidChart-v3.1.0\AndroidManifest.xml:16:5-18:41
MERGED from [com.facebook.shimmer:shimmer:0.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\0f679c305182e03100d64ad422788b57\transformed\jetified-shimmer-0.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.shimmer:shimmer:0.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\0f679c305182e03100d64ad422788b57\transformed\jetified-shimmer-0.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4143901f208cdfa6ffbcfacd2d0a9d1\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4143901f208cdfa6ffbcfacd2d0a9d1\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3dcdef5b9f04aee58a418e24c6945f8\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3dcdef5b9f04aee58a418e24c6945f8\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:gifdecoder:4.14.2] C:\Users\<USER>\.gradle\caches\transforms-3\d97c3db64fd63c39af8a8f807d4f9588\transformed\jetified-gifdecoder-4.14.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.14.2] C:\Users\<USER>\.gradle\caches\transforms-3\d97c3db64fd63c39af8a8f807d4f9588\transformed\jetified-gifdecoder-4.14.2\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.exifinterface:exifinterface:1.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\0c5fef66de8c362a273c2a6af5439677\transformed\exifinterface-1.3.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\0c5fef66de8c362a273c2a6af5439677\transformed\exifinterface-1.3.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\c900719aaf192cd5689ec37a91c858c8\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\c900719aaf192cd5689ec37a91c858c8\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c2d2042dcaf0377e763c5906335d1b53\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c2d2042dcaf0377e763c5906335d1b53\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\237161b9ad839da30c99f15645867c90\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\237161b9ad839da30c99f15645867c90\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-components:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\80f9112ff9b4f6703f8e86c4a16dca98\transformed\jetified-firebase-components-17.1.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-components:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\80f9112ff9b4f6703f8e86c4a16dca98\transformed\jetified-firebase-components-17.1.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\23890166d6a8b14dee3091bd6ffdea5c\transformed\jetified-billing-6.0.1\AndroidManifest.xml:6:5-8:41
MERGED from [com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\23890166d6a8b14dee3091bd6ffdea5c\transformed\jetified-billing-6.0.1\AndroidManifest.xml:6:5-8:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e0f5c7c79df0748d229f2beda36ab74d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e0f5c7c79df0748d229f2beda36ab74d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e3b38192b92eeb3fc9e8187da50ce6a9\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e3b38192b92eeb3fc9e8187da50ce6a9\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b28104446ff0f3d3f5e4b1c7861cccc0\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b28104446ff0f3d3f5e4b1c7861cccc0\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\06bbd20a458f343f77beb3bb8e73f53d\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\06bbd20a458f343f77beb3bb8e73f53d\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b2cfc7cca7cf941fb5a48ea556764a5\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b2cfc7cca7cf941fb5a48ea556764a5\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.datatransport:transport-api:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2090c19d7b5dec67bde921689114a109\transformed\jetified-transport-api-3.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2090c19d7b5dec67bde921689114a109\transformed\jetified-transport-api-3.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\13dad629b9d828476a8b326a2ac54af6\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\13dad629b9d828476a8b326a2ac54af6\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\86d41ed1b81d8bd9d2260149f60eb3e3\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\86d41ed1b81d8bd9d2260149f60eb3e3\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.maps.android:android-maps-utils:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d812f540c2906d43adfa5531b198632c\transformed\jetified-android-maps-utils-2.2.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.maps.android:android-maps-utils:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d812f540c2906d43adfa5531b198632c\transformed\jetified-android-maps-utils-2.2.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.github.duanhong169:colorpicker:1.1.6] C:\Users\<USER>\.gradle\caches\transforms-3\9a1d3a692c53e362f30caa998387687c\transformed\jetified-colorpicker-1.1.6\AndroidManifest.xml:7:5-9:41
MERGED from [com.github.duanhong169:colorpicker:1.1.6] C:\Users\<USER>\.gradle\caches\transforms-3\9a1d3a692c53e362f30caa998387687c\transformed\jetified-colorpicker-1.1.6\AndroidManifest.xml:7:5-9:41
MERGED from [com.github.duanhong169:checkerboarddrawable:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\2b842736fcb536cdd2b6459f437e2379\transformed\jetified-checkerboarddrawable-1.0.2\AndroidManifest.xml:7:5-9:41
MERGED from [com.github.duanhong169:checkerboarddrawable:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\2b842736fcb536cdd2b6459f437e2379\transformed\jetified-checkerboarddrawable-1.0.2\AndroidManifest.xml:7:5-9:41
MERGED from [com.intuit.sdp:sdp-android:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c1298cb649060719aeb7e0babf1b9f7c\transformed\jetified-sdp-android-1.1.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.intuit.sdp:sdp-android:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c1298cb649060719aeb7e0babf1b9f7c\transformed\jetified-sdp-android-1.1.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.intuit.ssp:ssp-android:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\fe1f3fa813fd8988796c41e4c70b5f0f\transformed\jetified-ssp-android-1.1.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.intuit.ssp:ssp-android:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\fe1f3fa813fd8988796c41e4c70b5f0f\transformed\jetified-ssp-android-1.1.0\AndroidManifest.xml:7:5-9:41
MERGED from [pl.droidsonroids.gif:android-gif-drawable:1.2.22] C:\Users\<USER>\.gradle\caches\transforms-3\92466a5cfd4dfe9238e8cc1f003a2347\transformed\jetified-android-gif-drawable-1.2.22\AndroidManifest.xml:5:5-7:41
MERGED from [pl.droidsonroids.gif:android-gif-drawable:1.2.22] C:\Users\<USER>\.gradle\caches\transforms-3\92466a5cfd4dfe9238e8cc1f003a2347\transformed\jetified-android-gif-drawable-1.2.22\AndroidManifest.xml:5:5-7:41
MERGED from [com.android.volley:volley:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\d2aa71aa11f556cb890e82e42e162595\transformed\jetified-volley-1.2.1\AndroidManifest.xml:5:5-43
MERGED from [com.android.volley:volley:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\d2aa71aa11f556cb890e82e42e162595\transformed\jetified-volley-1.2.1\AndroidManifest.xml:5:5-43
MERGED from [com.google.android.play:core-common:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\dc72e552015300076bb41fda69eedbd2\transformed\jetified-core-common-2.0.2\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.play:core-common:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\dc72e552015300076bb41fda69eedbd2\transformed\jetified-core-common-2.0.2\AndroidManifest.xml:7:5-9:41
MERGED from [pl.droidsonroids.relinker:relinker:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\0d00c90549ca0083eb4a8142f3073b8b\transformed\jetified-relinker-1.4.1\AndroidManifest.xml:5:5-43
MERGED from [pl.droidsonroids.relinker:relinker:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\0d00c90549ca0083eb4a8142f3073b8b\transformed\jetified-relinker-1.4.1\AndroidManifest.xml:5:5-43
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\transforms-3\d232e3111dc78fa8c6f2ad38a2151a17\transformed\jetified-image-1.0.0-beta1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\transforms-3\d232e3111dc78fa8c6f2ad38a2151a17\transformed\jetified-image-1.0.0-beta1\AndroidManifest.xml:5:5-44
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml
activity#com.stripe.android.view.AddPaymentMethodActivity
ADDED from [com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:12:9-14:57
	android:theme
		ADDED from [com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:14:13-54
	android:name
		ADDED from [com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:13:13-76
activity#com.stripe.android.view.PaymentMethodsActivity
ADDED from [com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:15:9-17:57
	android:theme
		ADDED from [com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:17:13-54
	android:name
		ADDED from [com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:16:13-74
activity#com.stripe.android.view.PaymentFlowActivity
ADDED from [com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:18:9-20:57
	android:theme
		ADDED from [com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:20:13-54
	android:name
		ADDED from [com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:19:13-71
activity#com.stripe.android.view.PaymentAuthWebViewActivity
ADDED from [com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:21:9-23:57
	android:theme
		ADDED from [com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:23:13-54
	android:name
		ADDED from [com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:22:13-78
activity#com.stripe.android.view.PaymentRelayActivity
ADDED from [com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:24:9-26:57
	android:theme
		ADDED from [com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:26:13-54
	android:name
		ADDED from [com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:25:13-72
activity#com.stripe.android.view.Stripe3ds2CompletionActivity
ADDED from [com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:27:9-29:57
	android:theme
		ADDED from [com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:29:13-54
	android:name
		ADDED from [com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:28:13-80
activity#com.stripe.android.stripe3ds2.views.ChallengeActivity
ADDED from [com.stripe:stripe-3ds2-android:2.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\7f344a36bb06c1b19b3d8dc344e8ef3c\transformed\jetified-stripe-3ds2-android-2.7.2\AndroidManifest.xml:12:9-14:54
	android:theme
		ADDED from [com.stripe:stripe-3ds2-android:2.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\7f344a36bb06c1b19b3d8dc344e8ef3c\transformed\jetified-stripe-3ds2-android-2.7.2\AndroidManifest.xml:14:13-51
	android:name
		ADDED from [com.stripe:stripe-3ds2-android:2.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\7f344a36bb06c1b19b3d8dc344e8ef3c\transformed\jetified-stripe-3ds2-android-2.7.2\AndroidManifest.xml:13:13-81
activity#com.stripe.android.stripe3ds2.views.ChallengeProgressDialogActivity
ADDED from [com.stripe:stripe-3ds2-android:2.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\7f344a36bb06c1b19b3d8dc344e8ef3c\transformed\jetified-stripe-3ds2-android-2.7.2\AndroidManifest.xml:15:9-17:54
	android:theme
		ADDED from [com.stripe:stripe-3ds2-android:2.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\7f344a36bb06c1b19b3d8dc344e8ef3c\transformed\jetified-stripe-3ds2-android-2.7.2\AndroidManifest.xml:17:13-51
	android:name
		ADDED from [com.stripe:stripe-3ds2-android:2.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\7f344a36bb06c1b19b3d8dc344e8ef3c\transformed\jetified-stripe-3ds2-android-2.7.2\AndroidManifest.xml:16:13-95
activity#com.karumi.dexter.DexterActivity
ADDED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\dfce37622a563a24f0c8e9b3351fbc6a\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:27:9-29:72
	android:theme
		ADDED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\dfce37622a563a24f0c8e9b3351fbc6a\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\dfce37622a563a24f0c8e9b3351fbc6a\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:28:13-60
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from [com.google.android.libraries.places:places:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\245b102fe60f0031ab708afcac9ce0b9\transformed\jetified-places-3.0.0\AndroidManifest.xml:11:5-76
	android:name
		ADDED from [com.google.android.libraries.places:places:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\245b102fe60f0031ab708afcac9ce0b9\transformed\jetified-places-3.0.0\AndroidManifest.xml:11:22-73
activity#com.google.android.libraries.places.widget.AutocompleteActivity
ADDED from [com.google.android.libraries.places:places:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\245b102fe60f0031ab708afcac9ce0b9\transformed\jetified-places-3.0.0\AndroidManifest.xml:15:9-21:20
	android:label
		ADDED from [com.google.android.libraries.places:places:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\245b102fe60f0031ab708afcac9ce0b9\transformed\jetified-places-3.0.0\AndroidManifest.xml:18:13-62
	android:windowSoftInputMode
		ADDED from [com.google.android.libraries.places:places:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\245b102fe60f0031ab708afcac9ce0b9\transformed\jetified-places-3.0.0\AndroidManifest.xml:20:13-55
	android:exported
		ADDED from [com.google.android.libraries.places:places:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\245b102fe60f0031ab708afcac9ce0b9\transformed\jetified-places-3.0.0\AndroidManifest.xml:17:13-37
	android:theme
		ADDED from [com.google.android.libraries.places:places:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\245b102fe60f0031ab708afcac9ce0b9\transformed\jetified-places-3.0.0\AndroidManifest.xml:19:13-61
	android:name
		ADDED from [com.google.android.libraries.places:places:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\245b102fe60f0031ab708afcac9ce0b9\transformed\jetified-places-3.0.0\AndroidManifest.xml:16:13-91
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f6fa1e240580d875b9faf8d016b434a\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f6fa1e240580d875b9faf8d016b434a\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f6fa1e240580d875b9faf8d016b434a\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f6fa1e240580d875b9faf8d016b434a\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f6fa1e240580d875b9faf8d016b434a\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f6fa1e240580d875b9faf8d016b434a\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f6fa1e240580d875b9faf8d016b434a\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f6fa1e240580d875b9faf8d016b434a\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f6fa1e240580d875b9faf8d016b434a\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f6fa1e240580d875b9faf8d016b434a\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:34:13-89
uses-feature#0x00020000
ADDED from [com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bca5bb2c25cf61ac91ecd47130eaf591\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:26:5-28:35
	android:glEsVersion
		ADDED from [com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bca5bb2c25cf61ac91ecd47130eaf591\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:27:9-41
	android:required
		ADDED from [com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bca5bb2c25cf61ac91ecd47130eaf591\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:28:9-32
package#com.google.android.apps.maps
ADDED from [com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bca5bb2c25cf61ac91ecd47130eaf591\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:33:9-64
	android:name
		ADDED from [com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bca5bb2c25cf61ac91ecd47130eaf591\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:33:18-61
uses-library#org.apache.http.legacy
ADDED from [com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bca5bb2c25cf61ac91ecd47130eaf591\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:39:9-41:40
	android:required
		ADDED from [com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bca5bb2c25cf61ac91ecd47130eaf591\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:41:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bca5bb2c25cf61ac91ecd47130eaf591\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:40:13-50
meta-data#com.google.gms.fitness.sdk.version
ADDED from [com.google.android.gms:play-services-fitness:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4018ae0a1966300199c2b3e05a6d81e9\transformed\jetified-play-services-fitness-20.0.0\AndroidManifest.xml:23:9-25:38
	android:value
		ADDED from [com.google.android.gms:play-services-fitness:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4018ae0a1966300199c2b3e05a6d81e9\transformed\jetified-play-services-fitness-20.0.0\AndroidManifest.xml:25:13-35
	android:name
		ADDED from [com.google.android.gms:play-services-fitness:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4018ae0a1966300199c2b3e05a6d81e9\transformed\jetified-play-services-fitness-20.0.0\AndroidManifest.xml:24:13-62
service#com.google.mlkit.common.internal.MlKitComponentDiscoveryService
ADDED from [com.google.android.gms:play-services-mlkit-text-recognition-common:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b4bd5f6b063655bfefc8564cd709cc40\transformed\jetified-play-services-mlkit-text-recognition-common-18.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:vision-common:17.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c3691d46c450c8405e6a322b681b222a\transformed\jetified-vision-common-17.2.1\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:vision-common:17.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c3691d46c450c8405e6a322b681b222a\transformed\jetified-vision-common-17.2.1\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c505a1ac40223f3f4347c0c22e87fce\transformed\jetified-common-18.5.0\AndroidManifest.xml:15:9-23:19
MERGED from [com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c505a1ac40223f3f4347c0c22e87fce\transformed\jetified-common-18.5.0\AndroidManifest.xml:15:9-23:19
	android:exported
		ADDED from [com.google.android.gms:play-services-mlkit-text-recognition-common:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b4bd5f6b063655bfefc8564cd709cc40\transformed\jetified-play-services-mlkit-text-recognition-common-18.0.0\AndroidManifest.xml:11:13-37
	tools:targetApi
		ADDED from [com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c505a1ac40223f3f4347c0c22e87fce\transformed\jetified-common-18.5.0\AndroidManifest.xml:19:13-32
	android:directBootAware
		ADDED from [com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c505a1ac40223f3f4347c0c22e87fce\transformed\jetified-common-18.5.0\AndroidManifest.xml:17:13-43
	android:name
		ADDED from [com.google.android.gms:play-services-mlkit-text-recognition-common:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b4bd5f6b063655bfefc8564cd709cc40\transformed\jetified-play-services-mlkit-text-recognition-common-18.0.0\AndroidManifest.xml:10:13-91
meta-data#com.google.firebase.components:com.google.mlkit.vision.text.internal.TextRegistrar
ADDED from [com.google.android.gms:play-services-mlkit-text-recognition-common:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b4bd5f6b063655bfefc8564cd709cc40\transformed\jetified-play-services-mlkit-text-recognition-common-18.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.android.gms:play-services-mlkit-text-recognition-common:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b4bd5f6b063655bfefc8564cd709cc40\transformed\jetified-play-services-mlkit-text-recognition-common-18.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-mlkit-text-recognition-common:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b4bd5f6b063655bfefc8564cd709cc40\transformed\jetified-play-services-mlkit-text-recognition-common-18.0.0\AndroidManifest.xml:13:17-114
meta-data#com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar
ADDED from [com.google.mlkit:vision-common:17.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c3691d46c450c8405e6a322b681b222a\transformed\jetified-vision-common-17.2.1\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.mlkit:vision-common:17.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c3691d46c450c8405e6a322b681b222a\transformed\jetified-vision-common-17.2.1\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.mlkit:vision-common:17.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c3691d46c450c8405e6a322b681b222a\transformed\jetified-vision-common-17.2.1\AndroidManifest.xml:13:17-124
provider#com.google.mlkit.common.internal.MlKitInitProvider
ADDED from [com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c505a1ac40223f3f4347c0c22e87fce\transformed\jetified-common-18.5.0\AndroidManifest.xml:9:9-13:38
	android:authorities
		ADDED from [com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c505a1ac40223f3f4347c0c22e87fce\transformed\jetified-common-18.5.0\AndroidManifest.xml:11:13-69
	android:exported
		ADDED from [com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c505a1ac40223f3f4347c0c22e87fce\transformed\jetified-common-18.5.0\AndroidManifest.xml:12:13-37
	android:initOrder
		ADDED from [com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c505a1ac40223f3f4347c0c22e87fce\transformed\jetified-common-18.5.0\AndroidManifest.xml:13:13-35
	android:name
		ADDED from [com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c505a1ac40223f3f4347c0c22e87fce\transformed\jetified-common-18.5.0\AndroidManifest.xml:10:13-78
meta-data#com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar
ADDED from [com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c505a1ac40223f3f4347c0c22e87fce\transformed\jetified-common-18.5.0\AndroidManifest.xml:20:13-22:85
	android:value
		ADDED from [com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c505a1ac40223f3f4347c0c22e87fce\transformed\jetified-common-18.5.0\AndroidManifest.xml:22:17-82
	android:name
		ADDED from [com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c505a1ac40223f3f4347c0c22e87fce\transformed\jetified-common-18.5.0\AndroidManifest.xml:21:17-120
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-auth-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\15d4f429e740d2d9a84daaaefe71c783\transformed\jetified-firebase-auth-ktx-21.1.0\AndroidManifest.xml:8:9-14:19
MERGED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:64:9-70:19
MERGED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:64:9-70:19
MERGED from [com.google.firebase:firebase-messaging-ktx:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\c80935087eb2baf2ce8d5fda4c1695fb\transformed\jetified-firebase-messaging-ktx-23.1.1\AndroidManifest.xml:25:9-31:19
MERGED from [com.google.firebase:firebase-messaging-ktx:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\c80935087eb2baf2ce8d5fda4c1695fb\transformed\jetified-firebase-messaging-ktx-23.1.1\AndroidManifest.xml:25:9-31:19
MERGED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:52:9-58:19
MERGED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:52:9-58:19
MERGED from [com.google.firebase:firebase-crashlytics-ktx:18.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\7a08c1f471d4aed10f5a62d4bdfd7cc0\transformed\jetified-firebase-crashlytics-ktx-18.3.5\AndroidManifest.xml:23:9-29:19
MERGED from [com.google.firebase:firebase-crashlytics-ktx:18.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\7a08c1f471d4aed10f5a62d4bdfd7cc0\transformed\jetified-firebase-crashlytics-ktx-18.3.5\AndroidManifest.xml:23:9-29:19
MERGED from [com.google.firebase:firebase-analytics-ktx:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d0398be7ec17550451ee5b14d880cfc\transformed\jetified-firebase-analytics-ktx-21.2.0\AndroidManifest.xml:8:9-14:19
MERGED from [com.google.firebase:firebase-analytics-ktx:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d0398be7ec17550451ee5b14d880cfc\transformed\jetified-firebase-analytics-ktx-21.2.0\AndroidManifest.xml:8:9-14:19
MERGED from [com.google.firebase:firebase-common-ktx:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b2a903c9b0acab1e7698691d1a2c986\transformed\jetified-firebase-common-ktx-20.3.0\AndroidManifest.xml:11:9-17:19
MERGED from [com.google.firebase:firebase-common-ktx:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b2a903c9b0acab1e7698691d1a2c986\transformed\jetified-firebase-common-ktx-20.3.0\AndroidManifest.xml:11:9-17:19
MERGED from [com.google.firebase:firebase-crashlytics:18.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\967d8355743d170a894a29b4b1a80382\transformed\jetified-firebase-crashlytics-18.3.5\AndroidManifest.xml:14:9-20:19
MERGED from [com.google.firebase:firebase-crashlytics:18.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\967d8355743d170a894a29b4b1a80382\transformed\jetified-firebase-crashlytics-18.3.5\AndroidManifest.xml:14:9-20:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\168037942f327435dcf9fbba3d0dde22\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\168037942f327435dcf9fbba3d0dde22\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.firebase:firebase-installations:17.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\f175c9d0e7eca696c235389c9c14325d\transformed\jetified-firebase-installations-17.1.2\AndroidManifest.xml:14:9-20:19
MERGED from [com.google.firebase:firebase-installations:17.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\f175c9d0e7eca696c235389c9c14325d\transformed\jetified-firebase-installations-17.1.2\AndroidManifest.xml:14:9-20:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\b6cf1c5e33857039c8761daab9336c00\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:24:9-30:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\b6cf1c5e33857039c8761daab9336c00\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:24:9-30:19
MERGED from [com.google.firebase:firebase-common:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\422e0883ce3188039e67d46c46566547\transformed\jetified-firebase-common-20.3.0\AndroidManifest.xml:32:9-36:35
MERGED from [com.google.firebase:firebase-common:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\422e0883ce3188039e67d46c46566547\transformed\jetified-firebase-common-20.3.0\AndroidManifest.xml:32:9-36:35
	android:exported
		ADDED from [com.google.firebase:firebase-auth-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\15d4f429e740d2d9a84daaaefe71c783\transformed\jetified-firebase-auth-ktx-21.1.0\AndroidManifest.xml:10:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\422e0883ce3188039e67d46c46566547\transformed\jetified-firebase-common-20.3.0\AndroidManifest.xml:36:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\422e0883ce3188039e67d46c46566547\transformed\jetified-firebase-common-20.3.0\AndroidManifest.xml:34:13-43
	android:name
		ADDED from [com.google.firebase:firebase-auth-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\15d4f429e740d2d9a84daaaefe71c783\transformed\jetified-firebase-auth-ktx-21.1.0\AndroidManifest.xml:9:13-84
meta-data#com.google.firebase.components:com.google.firebase.auth.ktx.FirebaseAuthKtxRegistrar
ADDED from [com.google.firebase:firebase-auth-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\15d4f429e740d2d9a84daaaefe71c783\transformed\jetified-firebase-auth-ktx-21.1.0\AndroidManifest.xml:11:13-13:85
	android:value
		ADDED from [com.google.firebase:firebase-auth-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\15d4f429e740d2d9a84daaaefe71c783\transformed\jetified-firebase-auth-ktx-21.1.0\AndroidManifest.xml:13:17-82
	android:name
		ADDED from [com.google.firebase:firebase-auth-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\15d4f429e740d2d9a84daaaefe71c783\transformed\jetified-firebase-auth-ktx-21.1.0\AndroidManifest.xml:12:17-116
activity#com.google.firebase.auth.internal.GenericIdpActivity
ADDED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:27:9-44:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:29:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:31:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:30:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:32:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:28:13-80
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:genericidp
ADDED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:33:13-43:29
action#android.intent.action.VIEW
ADDED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:34:17-69
	android:name
		ADDED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:34:25-66
category#android.intent.category.DEFAULT
ADDED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:36:17-76
	android:name
		ADDED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:36:27-73
category#android.intent.category.BROWSABLE
ADDED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:37:17-78
	android:name
		ADDED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:37:27-75
data
ADDED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:39:17-42:51
	android:path
		ADDED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:41:21-37
	android:host
		ADDED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:40:21-49
	android:scheme
		ADDED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:42:21-48
activity#com.google.firebase.auth.internal.RecaptchaActivity
ADDED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:45:9-62:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:47:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:49:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:48:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:50:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:46:13-79
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:recaptcha
ADDED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:51:13-61:29
meta-data#com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar
ADDED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:67:13-69:85
	android:value
		ADDED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:69:17-82
	android:name
		ADDED from [com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:68:17-109
meta-data#com.google.firebase.components:com.google.firebase.messaging.ktx.FirebaseMessagingKtxRegistrar
ADDED from [com.google.firebase:firebase-messaging-ktx:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\c80935087eb2baf2ce8d5fda4c1695fb\transformed\jetified-firebase-messaging-ktx-23.1.1\AndroidManifest.xml:28:13-30:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging-ktx:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\c80935087eb2baf2ce8d5fda4c1695fb\transformed\jetified-firebase-messaging-ktx-23.1.1\AndroidManifest.xml:30:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging-ktx:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\c80935087eb2baf2ce8d5fda4c1695fb\transformed\jetified-firebase-messaging-ktx-23.1.1\AndroidManifest.xml:29:17-126
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:28:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\4d427ed86a6d184dccce4dcab3eba6c4\transformed\jetified-play-services-cloud-messaging-17.0.1\AndroidManifest.xml:11:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\4d427ed86a6d184dccce4dcab3eba6c4\transformed\jetified-play-services-cloud-messaging-17.0.1\AndroidManifest.xml:11:5-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:28:22-79
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:31:9-38:20
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:33:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:34:13-73
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:32:13-78
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:35:13-37:29
action#com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:36:17-81
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:36:25-78
service#com.google.firebase.messaging.FirebaseMessagingService
ADDED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:44:9-51:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:47:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:46:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:45:13-82
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar
ADDED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:55:13-57:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:57:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:56:17-119
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5296fb8d536108245faa30c6d8197de1\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5296fb8d536108245faa30c6d8197de1\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5296fb8d536108245faa30c6d8197de1\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5296fb8d536108245faa30c6d8197de1\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:19-85
meta-data#com.google.firebase.components:com.google.firebase.crashlytics.ktx.FirebaseCrashlyticsKtxRegistrar
ADDED from [com.google.firebase:firebase-crashlytics-ktx:18.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\7a08c1f471d4aed10f5a62d4bdfd7cc0\transformed\jetified-firebase-crashlytics-ktx-18.3.5\AndroidManifest.xml:26:13-28:85
	android:value
		ADDED from [com.google.firebase:firebase-crashlytics-ktx:18.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\7a08c1f471d4aed10f5a62d4bdfd7cc0\transformed\jetified-firebase-crashlytics-ktx-18.3.5\AndroidManifest.xml:28:17-82
	android:name
		ADDED from [com.google.firebase:firebase-crashlytics-ktx:18.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\7a08c1f471d4aed10f5a62d4bdfd7cc0\transformed\jetified-firebase-crashlytics-ktx-18.3.5\AndroidManifest.xml:27:17-130
meta-data#com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsKtxRegistrar
ADDED from [com.google.firebase:firebase-analytics-ktx:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d0398be7ec17550451ee5b14d880cfc\transformed\jetified-firebase-analytics-ktx-21.2.0\AndroidManifest.xml:11:13-13:85
	android:value
		ADDED from [com.google.firebase:firebase-analytics-ktx:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d0398be7ec17550451ee5b14d880cfc\transformed\jetified-firebase-analytics-ktx-21.2.0\AndroidManifest.xml:13:17-82
	android:name
		ADDED from [com.google.firebase:firebase-analytics-ktx:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d0398be7ec17550451ee5b14d880cfc\transformed\jetified-firebase-analytics-ktx-21.2.0\AndroidManifest.xml:12:17-126
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b2a903c9b0acab1e7698691d1a2c986\transformed\jetified-firebase-common-ktx-20.3.0\AndroidManifest.xml:14:13-16:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b2a903c9b0acab1e7698691d1a2c986\transformed\jetified-firebase-common-ktx-20.3.0\AndroidManifest.xml:16:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b2a903c9b0acab1e7698691d1a2c986\transformed\jetified-firebase-common-ktx-20.3.0\AndroidManifest.xml:15:17-113
meta-data#com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar
ADDED from [com.google.firebase:firebase-crashlytics:18.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\967d8355743d170a894a29b4b1a80382\transformed\jetified-firebase-crashlytics-18.3.5\AndroidManifest.xml:17:13-19:85
	android:value
		ADDED from [com.google.firebase:firebase-crashlytics:18.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\967d8355743d170a894a29b4b1a80382\transformed\jetified-firebase-crashlytics-18.3.5\AndroidManifest.xml:19:17-82
	android:name
		ADDED from [com.google.firebase:firebase-crashlytics:18.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\967d8355743d170a894a29b4b1a80382\transformed\jetified-firebase-crashlytics-18.3.5\AndroidManifest.xml:18:17-115
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\168037942f327435dcf9fbba3d0dde22\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ebc0a470e1cbd9a5ad219bc5fd5997fd\transformed\jetified-play-services-measurement-impl-21.2.0\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ebc0a470e1cbd9a5ad219bc5fd5997fd\transformed\jetified-play-services-measurement-impl-21.2.0\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4471cce94b29aeb47706814ba2b6c808\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4471cce94b29aeb47706814ba2b6c808\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\195d9f9b0cb87448c1e03b6bfc41a24b\transformed\jetified-play-services-measurement-sdk-api-21.2.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\195d9f9b0cb87448c1e03b6bfc41a24b\transformed\jetified-play-services-measurement-sdk-api-21.2.0\AndroidManifest.xml:26:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\168037942f327435dcf9fbba3d0dde22\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:25:22-76
meta-data#com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar
ADDED from [com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\168037942f327435dcf9fbba3d0dde22\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:31:13-33:85
	android:value
		ADDED from [com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\168037942f327435dcf9fbba3d0dde22\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:33:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\168037942f327435dcf9fbba3d0dde22\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:32:17-139
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:17.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\f175c9d0e7eca696c235389c9c14325d\transformed\jetified-firebase-installations-17.1.2\AndroidManifest.xml:17:13-19:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\f175c9d0e7eca696c235389c9c14325d\transformed\jetified-firebase-installations-17.1.2\AndroidManifest.xml:19:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\f175c9d0e7eca696c235389c9c14325d\transformed\jetified-firebase-installations-17.1.2\AndroidManifest.xml:18:17-127
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\b6cf1c5e33857039c8761daab9336c00\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:27:13-29:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\b6cf1c5e33857039c8761daab9336c00\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:29:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\b6cf1c5e33857039c8761daab9336c00\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:28:17-115
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\422e0883ce3188039e67d46c46566547\transformed\jetified-firebase-common-20.3.0\AndroidManifest.xml:25:9-30:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\422e0883ce3188039e67d46c46566547\transformed\jetified-firebase-common-20.3.0\AndroidManifest.xml:27:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\422e0883ce3188039e67d46c46566547\transformed\jetified-firebase-common-20.3.0\AndroidManifest.xml:29:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\422e0883ce3188039e67d46c46566547\transformed\jetified-firebase-common-20.3.0\AndroidManifest.xml:28:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\422e0883ce3188039e67d46c46566547\transformed\jetified-firebase-common-20.3.0\AndroidManifest.xml:30:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\422e0883ce3188039e67d46c46566547\transformed\jetified-firebase-common-20.3.0\AndroidManifest.xml:26:13-77
intent#action:name:androidx.camera.extensions.action.VENDOR_ACTION
ADDED from [androidx.camera:camera-extensions:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\54b9491a3c7a79413b727cd19f2cf1e8\transformed\jetified-camera-extensions-1.2.2\AndroidManifest.xml:23:9-25:18
action#androidx.camera.extensions.action.VENDOR_ACTION
ADDED from [androidx.camera:camera-extensions:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\54b9491a3c7a79413b727cd19f2cf1e8\transformed\jetified-camera-extensions-1.2.2\AndroidManifest.xml:24:13-86
	android:name
		ADDED from [androidx.camera:camera-extensions:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\54b9491a3c7a79413b727cd19f2cf1e8\transformed\jetified-camera-extensions-1.2.2\AndroidManifest.xml:24:21-83
uses-library#androidx.camera.extensions.impl
ADDED from [androidx.camera:camera-extensions:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\54b9491a3c7a79413b727cd19f2cf1e8\transformed\jetified-camera-extensions-1.2.2\AndroidManifest.xml:29:9-31:40
	android:required
		ADDED from [androidx.camera:camera-extensions:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\54b9491a3c7a79413b727cd19f2cf1e8\transformed\jetified-camera-extensions-1.2.2\AndroidManifest.xml:31:13-37
	android:name
		ADDED from [androidx.camera:camera-extensions:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\54b9491a3c7a79413b727cd19f2cf1e8\transformed\jetified-camera-extensions-1.2.2\AndroidManifest.xml:30:13-59
service#androidx.camera.core.impl.MetadataHolderService
ADDED from [androidx.camera:camera-camera2:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\ee28ca93c07bf81d90ff8f6019728918\transformed\jetified-camera-camera2-1.2.2\AndroidManifest.xml:24:9-33:19
MERGED from [androidx.camera:camera-core:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2f30601676b1559d6170091243eee16\transformed\jetified-camera-core-1.2.2\AndroidManifest.xml:29:9-33:78
MERGED from [androidx.camera:camera-core:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2f30601676b1559d6170091243eee16\transformed\jetified-camera-core-1.2.2\AndroidManifest.xml:29:9-33:78
	tools:node
		ADDED from [androidx.camera:camera-camera2:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\ee28ca93c07bf81d90ff8f6019728918\transformed\jetified-camera-camera2-1.2.2\AndroidManifest.xml:29:13-31
	android:enabled
		ADDED from [androidx.camera:camera-camera2:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\ee28ca93c07bf81d90ff8f6019728918\transformed\jetified-camera-camera2-1.2.2\AndroidManifest.xml:26:13-36
	android:exported
		ADDED from [androidx.camera:camera-camera2:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\ee28ca93c07bf81d90ff8f6019728918\transformed\jetified-camera-camera2-1.2.2\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.camera:camera-camera2:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\ee28ca93c07bf81d90ff8f6019728918\transformed\jetified-camera-camera2-1.2.2\AndroidManifest.xml:28:13-75
	android:name
		ADDED from [androidx.camera:camera-camera2:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\ee28ca93c07bf81d90ff8f6019728918\transformed\jetified-camera-camera2-1.2.2\AndroidManifest.xml:25:13-75
meta-data#androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER
ADDED from [androidx.camera:camera-camera2:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\ee28ca93c07bf81d90ff8f6019728918\transformed\jetified-camera-camera2-1.2.2\AndroidManifest.xml:30:13-32:89
	android:value
		ADDED from [androidx.camera:camera-camera2:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\ee28ca93c07bf81d90ff8f6019728918\transformed\jetified-camera-camera2-1.2.2\AndroidManifest.xml:32:17-86
	android:name
		ADDED from [androidx.camera:camera-camera2:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\ee28ca93c07bf81d90ff8f6019728918\transformed\jetified-camera-camera2-1.2.2\AndroidManifest.xml:31:17-103
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ebc0a470e1cbd9a5ad219bc5fd5997fd\transformed\jetified-play-services-measurement-impl-21.2.0\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ebc0a470e1cbd9a5ad219bc5fd5997fd\transformed\jetified-play-services-measurement-impl-21.2.0\AndroidManifest.xml:26:5-110
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:26:22-107
receiver#com.google.android.gms.measurement.AppMeasurementReceiver
ADDED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:29:9-33:20
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:31:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:30:13-85
service#com.google.android.gms.measurement.AppMeasurementService
ADDED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:35:9-38:40
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:38:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:36:13-84
service#com.google.android.gms.measurement.AppMeasurementJobService
ADDED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:39:9-43:72
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:41:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:42:13-37
	android:permission
		ADDED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:43:13-69
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:40:13-87
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\549ede32db9c3acb303de4fa6d2a7dfa\transformed\jetified-ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\549ede32db9c3acb303de4fa6d2a7dfa\transformed\jetified-ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\549ede32db9c3acb303de4fa6d2a7dfa\transformed\jetified-ui-tooling-release\AndroidManifest.xml:24:13-71
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\7ada1d27f1fad82fa455b667be34e866\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\078811cffc78e28bc7870a96ed097257\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\078811cffc78e28bc7870a96ed097257\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\c900719aaf192cd5689ec37a91c858c8\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\c900719aaf192cd5689ec37a91c858c8\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\7ada1d27f1fad82fa455b667be34e866\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\7ada1d27f1fad82fa455b667be34e866\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\7ada1d27f1fad82fa455b667be34e866\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\7ada1d27f1fad82fa455b667be34e866\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\7ada1d27f1fad82fa455b667be34e866\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\7ada1d27f1fad82fa455b667be34e866\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\7ada1d27f1fad82fa455b667be34e866\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\078811cffc78e28bc7870a96ed097257\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\078811cffc78e28bc7870a96ed097257\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\078811cffc78e28bc7870a96ed097257\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\415c161ff15d16996587df02a2a7fdc1\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\415c161ff15d16996587df02a2a7fdc1\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\415c161ff15d16996587df02a2a7fdc1\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\415c161ff15d16996587df02a2a7fdc1\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\415c161ff15d16996587df02a2a7fdc1\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\415c161ff15d16996587df02a2a7fdc1\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4b7543783d7e597efaf2f46cf5f3847\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4b7543783d7e597efaf2f46cf5f3847\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4b7543783d7e597efaf2f46cf5f3847\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
permission#com.manaknight.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4b7543783d7e597efaf2f46cf5f3847\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4b7543783d7e597efaf2f46cf5f3847\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4b7543783d7e597efaf2f46cf5f3847\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4b7543783d7e597efaf2f46cf5f3847\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4b7543783d7e597efaf2f46cf5f3847\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
uses-permission#com.manaknight.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4b7543783d7e597efaf2f46cf5f3847\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4b7543783d7e597efaf2f46cf5f3847\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\3eddf5b8a9d33eb11d1830cb5e4fd1aa\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
MERGED from [com.google.maps.android:android-maps-utils:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d812f540c2906d43adfa5531b198632c\transformed\jetified-android-maps-utils-2.2.0\AndroidManifest.xml:12:9-14:69
MERGED from [com.google.maps.android:android-maps-utils:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d812f540c2906d43adfa5531b198632c\transformed\jetified-android-maps-utils-2.2.0\AndroidManifest.xml:12:9-14:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\3eddf5b8a9d33eb11d1830cb5e4fd1aa\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\3eddf5b8a9d33eb11d1830cb5e4fd1aa\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
intent#action:name:com.android.vending.billing.InAppBillingService.BIND
ADDED from [com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\23890166d6a8b14dee3091bd6ffdea5c\transformed\jetified-billing-6.0.1\AndroidManifest.xml:13:9-15:18
action#com.android.vending.billing.InAppBillingService.BIND
ADDED from [com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\23890166d6a8b14dee3091bd6ffdea5c\transformed\jetified-billing-6.0.1\AndroidManifest.xml:14:13-91
	android:name
		ADDED from [com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\23890166d6a8b14dee3091bd6ffdea5c\transformed\jetified-billing-6.0.1\AndroidManifest.xml:14:21-88
meta-data#com.google.android.play.billingclient.version
ADDED from [com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\23890166d6a8b14dee3091bd6ffdea5c\transformed\jetified-billing-6.0.1\AndroidManifest.xml:19:9-21:37
	android:value
		ADDED from [com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\23890166d6a8b14dee3091bd6ffdea5c\transformed\jetified-billing-6.0.1\AndroidManifest.xml:21:13-34
	android:name
		ADDED from [com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\23890166d6a8b14dee3091bd6ffdea5c\transformed\jetified-billing-6.0.1\AndroidManifest.xml:20:13-73
activity#com.android.billingclient.api.ProxyBillingActivity
ADDED from [com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\23890166d6a8b14dee3091bd6ffdea5c\transformed\jetified-billing-6.0.1\AndroidManifest.xml:23:9-27:75
	android:exported
		ADDED from [com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\23890166d6a8b14dee3091bd6ffdea5c\transformed\jetified-billing-6.0.1\AndroidManifest.xml:26:13-37
	android:configChanges
		ADDED from [com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\23890166d6a8b14dee3091bd6ffdea5c\transformed\jetified-billing-6.0.1\AndroidManifest.xml:25:13-96
	android:theme
		ADDED from [com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\23890166d6a8b14dee3091bd6ffdea5c\transformed\jetified-billing-6.0.1\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\23890166d6a8b14dee3091bd6ffdea5c\transformed\jetified-billing-6.0.1\AndroidManifest.xml:24:13-78
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e0f5c7c79df0748d229f2beda36ab74d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e3b38192b92eeb3fc9e8187da50ce6a9\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e3b38192b92eeb3fc9e8187da50ce6a9\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e0f5c7c79df0748d229f2beda36ab74d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e0f5c7c79df0748d229f2beda36ab74d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e0f5c7c79df0748d229f2beda36ab74d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e0f5c7c79df0748d229f2beda36ab74d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e0f5c7c79df0748d229f2beda36ab74d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e3b38192b92eeb3fc9e8187da50ce6a9\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e3b38192b92eeb3fc9e8187da50ce6a9\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e3b38192b92eeb3fc9e8187da50ce6a9\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e3b38192b92eeb3fc9e8187da50ce6a9\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e3b38192b92eeb3fc9e8187da50ce6a9\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e3b38192b92eeb3fc9e8187da50ce6a9\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e3b38192b92eeb3fc9e8187da50ce6a9\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
activity#com.google.android.play.core.common.PlayCoreDialogWrapperActivity
ADDED from [com.google.android.play:core-common:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\dc72e552015300076bb41fda69eedbd2\transformed\jetified-core-common-2.0.2\AndroidManifest.xml:14:9-18:65
	android:stateNotNeeded
		ADDED from [com.google.android.play:core-common:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\dc72e552015300076bb41fda69eedbd2\transformed\jetified-core-common-2.0.2\AndroidManifest.xml:17:13-42
	android:exported
		ADDED from [com.google.android.play:core-common:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\dc72e552015300076bb41fda69eedbd2\transformed\jetified-core-common-2.0.2\AndroidManifest.xml:16:13-37
	android:theme
		ADDED from [com.google.android.play:core-common:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\dc72e552015300076bb41fda69eedbd2\transformed\jetified-core-common-2.0.2\AndroidManifest.xml:18:13-62
	android:name
		ADDED from [com.google.android.play:core-common:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\dc72e552015300076bb41fda69eedbd2\transformed\jetified-core-common-2.0.2\AndroidManifest.xml:15:13-93
